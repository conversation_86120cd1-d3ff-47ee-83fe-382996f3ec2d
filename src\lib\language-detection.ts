// Language detection and utilities

export type SupportedLocale = 'en' | 'zh-TW' | 'zh-CN'

export function detectUserLanguage(): SupportedLocale {
  if (typeof window === 'undefined') return 'en'
  
  const browserLang = navigator.language || navigator.languages?.[0] || 'en'
  
  if (browserLang.startsWith('zh')) {
    if (browserLang.includes('TW') || browserLang.includes('HK')) {
      return 'zh-TW'
    }
    return 'zh-CN'
  }
  
  return 'en'
}

export function getLanguageDisplayName(locale: SupportedLocale): string {
  const names: Record<SupportedLocale, string> = {
    'en': 'English',
    'zh-TW': '繁體中文',
    'zh-CN': '简体中文'
  }
  return names[locale]
}

export function getLanguageFlag(locale: SupportedLocale): string {
  const flags: Record<SupportedLocale, string> = {
    'en': '🇺🇸',
    'zh-TW': '🇹🇼',
    'zh-CN': '🇨🇳'
  }
  return flags[locale]
}

export function isRTL(locale: SupportedLocale): boolean {
  // None of our supported languages are RTL
  return false
}

export function getAvailableLocales(): SupportedLocale[] {
  return ['en', 'zh-TW', 'zh-CN']
}
