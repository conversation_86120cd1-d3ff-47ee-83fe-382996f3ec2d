// Firebase SDK API 模組 - 替換手動 HTTP 調用
import { getFunctions, httpsCallable, Functions } from 'firebase/functions'
import { getFirebaseServices } from './firebase'

// Firebase Functions 實例
let functions: Functions | null = null

// 初始化 Firebase Functions
const initializeFunctions = () => {
  if (!functions) {
    const { app } = getFirebaseServices()
    if (!app) {
      throw new Error('Firebase app not initialized')
    }
    // 指定 asia-east1 區域
    functions = getFunctions(app, 'asia-east1')
  }
  return functions
}

// API 函數類型定義
interface GetUserDashboardResponse {
  success: boolean
  data: {
    user: {
      user_id: string
      email: string
      display_name: string
      created_at: string
      preferences: Record<string, any>
      onboarding_completed: boolean
      first_device_registered: boolean
    }
    subscription: {
      subscription_id: string
      plan: string
      status: string
      daily_limit_seconds: number
      current_day_used_seconds: number
      max_devices: number
      supported_platforms: string[]
      features: string[]
      register_region_timezone: string
      usage_reset_hour: number
      next_billing_date: string | null
      auto_renew: boolean
      stripe_customer_id: string | null
      stripe_subscription_id: string | null
    }
    devices: {
      active_devices: Array<{
        device_id: string
        device_name: string
        platform: string
        status: string
        is_authorized: boolean
        last_active_at: string
        created_at: string
        app_version: string
      }>
      active_sessions: any[]
      device_count: number
      active_session_count: number
    }
    // 注意：V2.3 已移除 usage 物件，只使用 subscription.current_day_used_seconds
  }
}

interface RemoveDeviceRequest {
  device_id: string
  reason?: string
}

interface RemoveDeviceResponse {
  success: boolean
  data: {
    device_id: string
    removed_at: string
    message: string
  }
}

interface CreateOrUpdateUserRequest {
  email: string
  display_name?: string
  profile_image?: string
  preferences?: Record<string, any>
}

interface CreateSubscriptionRequest {
  stripe_customer_id: string
  stripe_subscription_id: string
  plan_id: string
  status: string
  current_period_start: string
  current_period_end: string
  amount_paid: number
  payment_status: string
  period_start: string
  period_end: string
}

interface ValidateOrRegisterDeviceRequest {
  device_name: string
  platform: string
  app_version?: string
  device_info?: Record<string, any>
}

interface CheckUsageRequest {
  estimated_duration_seconds: number
}

interface SubmitUsageRequest {
  duration_seconds: number  // V2.3 更新：改名為 duration_seconds
  feature_type: string  // V2.3 新增：ai-speech-to-text, direct-speech-to-text
  device_id: string  // V2.3 新增
  platform: string  // V2.3 新增
  token_usage?: {  // V2.3 新增：OpenAI token 使用量
    // Chat Completion API (ai-mode)
    prompt_tokens?: number
    completion_tokens?: number
    total_tokens?: number
    // Realtime API (direct mode)
    input_tokens?: number
    output_tokens?: number
  }
  session_metadata?: {  // V2.3 新增：可選的會話元數據
    audio_quality?: string
    language?: string
    file_size_mb?: number
  }
}

// 創建所有 API 函數
export const firebaseApi = {
  // 獲取用戶 Dashboard 數據
  getUserDashboard: async (): Promise<GetUserDashboardResponse> => {
    const functions = initializeFunctions()
    const callable = httpsCallable<void, GetUserDashboardResponse>(functions, 'get_user_dashboard')
    
    console.log('🔄 Calling Firebase Function: get_user_dashboard')
    
    try {
      const result = await callable()
      console.log('✅ Firebase Function call successful')
      console.log('📥 FULL RESPONSE from get_user_dashboard:', JSON.stringify(result.data, null, 2))

      // 詳細檢查響應結構
      if (result.data) {
        console.log('🔍 Response structure analysis:')
        console.log('  - success:', result.data.success)
        console.log('  - data exists:', !!result.data.data)
        if (result.data.data) {
          console.log('  - user exists:', !!result.data.data.user)
          console.log('  - subscription exists:', !!result.data.data.subscription)
          console.log('  - devices exists:', !!result.data.data.devices)
          console.log('  - usage exists:', !!result.data.data.usage)
        }
      }

      return result.data
    } catch (error) {
      console.error('❌ Firebase Function error:', error)
      console.error('❌ Error details:', {
        code: error.code,
        message: error.message,
        details: error.details
      })
      throw error
    }
  },

  // 創建或更新用戶
  createOrUpdateUser: async (data: CreateOrUpdateUserRequest) => {
    const functions = initializeFunctions()
    const callable = httpsCallable(functions, 'create_or_update_user')
    
    console.log('🔄 Calling Firebase Function: create_or_update_user', data)
    
    try {
      const result = await callable(data)
      console.log('✅ Create/Update user successful:', result.data)
      return result.data
    } catch (error) {
      console.error('❌ Create/Update user error:', error)
      throw error
    }
  },

  // 移除設備
  removeDevice: async (data: RemoveDeviceRequest): Promise<RemoveDeviceResponse> => {
    const functions = initializeFunctions()
    const callable = httpsCallable<RemoveDeviceRequest, RemoveDeviceResponse>(functions, 'remove_device')
    
    console.log('🔄 Calling Firebase Function: remove_device', data)
    
    try {
      const result = await callable(data)
      console.log('✅ Remove device successful:', result.data)
      return result.data
    } catch (error) {
      console.error('❌ Remove device error:', error)
      throw error
    }
  },

  // 創建訂閱
  createSubscription: async (data: CreateSubscriptionRequest) => {
    const functions = initializeFunctions()
    const callable = httpsCallable(functions, 'create_subscription')
    
    console.log('🔄 Calling Firebase Function: create_subscription', data)
    
    try {
      const result = await callable(data)
      console.log('✅ Create subscription successful:', result.data)
      return result.data
    } catch (error) {
      console.error('❌ Create subscription error:', error)
      throw error
    }
  },

  // 驗證或註冊設備
  validateOrRegisterDevice: async (data: ValidateOrRegisterDeviceRequest) => {
    const functions = initializeFunctions()
    const callable = httpsCallable(functions, 'validate_or_register_device')
    
    console.log('🔄 Calling Firebase Function: validate_or_register_device', data)
    
    try {
      const result = await callable(data)
      console.log('✅ Validate/Register device successful:', result.data)
      return result.data
    } catch (error) {
      console.error('❌ Validate/Register device error:', error)
      throw error
    }
  },

  // 檢查使用量
  checkUsageBeforeRecording: async (data: CheckUsageRequest) => {
    const functions = initializeFunctions()
    const callable = httpsCallable(functions, 'check_usage_before_recording')
    
    console.log('🔄 Calling Firebase Function: check_usage_before_recording', data)
    
    try {
      const result = await callable(data)
      console.log('✅ Check usage successful:', result.data)
      return result.data
    } catch (error) {
      console.error('❌ Check usage error:', error)
      throw error
    }
  },

  // 提交使用量
  submitUsage: async (data: SubmitUsageRequest) => {
    const functions = initializeFunctions()
    const callable = httpsCallable(functions, 'submit_usage')
    
    console.log('🔄 Calling Firebase Function: submit_usage', data)
    
    try {
      const result = await callable(data)
      console.log('✅ Submit usage successful:', result.data)
      return result.data
    } catch (error) {
      console.error('❌ Submit usage error:', error)
      throw error
    }
  }
}

// 錯誤處理輔助函數
export const handleFirebaseError = (error: any) => {
  console.error('Firebase Function Error:', error)
  
  // Firebase Functions 錯誤格式
  if (error.code) {
    switch (error.code) {
      case 'functions/unauthenticated':
        return { message: '用戶未認證，請重新登入', code: 'UNAUTHENTICATED' }
      case 'functions/permission-denied':
        return { message: '權限不足', code: 'PERMISSION_DENIED' }
      case 'functions/not-found':
        return { message: '功能不存在', code: 'NOT_FOUND' }
      case 'functions/internal':
        return { message: '服務器內部錯誤', code: 'INTERNAL' }
      case 'functions/invalid-argument':
        return { message: '請求參數無效', code: 'INVALID_ARGUMENT' }
      default:
        return { message: error.message || '未知錯誤', code: error.code }
    }
  }
  
  return { message: '網絡錯誤，請稍後重試', code: 'NETWORK_ERROR' }
}
