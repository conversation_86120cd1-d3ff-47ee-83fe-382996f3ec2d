// Firebase Auth utilities
import { auth } from '@/lib/firebase'
import {
  signInWithPopup,
  GoogleAuthProvider,
  FacebookAuthProvider,
  OAuthProvider,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  User
} from 'firebase/auth'
// Note: Database operations removed - this is a web-only repository

const googleProvider = new GoogleAuthProvider()
const facebookProvider = new FacebookAuthProvider()
const appleProvider = new OAuthProvider('apple.com')

export interface AuthUser {
  uid: string
  email: string | null
  name: string | null
  image: string | null
  subscriptionPlan?: string
  subscriptionStatus?: string
}

// Generic sign in function
async function signInWithProvider(provider: GoogleAuthProvider | FacebookAuthProvider | OAuthProvider, providerName: string): Promise<{ success: boolean; user?: AuthUser; error?: string }> {
  try {
    // Clear any existing popup operations
    await auth.signOut().catch(() => {})

    console.log(`🚀 Starting ${providerName} sign-in...`)
    const result = await signInWithPopup(auth, provider)
    const user = result.user

    console.log(`✅ Firebase Auth User (${providerName}):`, {
      uid: user.uid,
      email: user.email,
      emailVerified: user.emailVerified,
      providerId: result.providerId
    })

    // Note: Database operations removed - this is a web-only repository
    // User management is handled by the backend team

    const authUser: AuthUser = {
      uid: user.uid,
      email: user.email,
      name: user.displayName,
      image: user.photoURL,
    }

    return { success: true, user: authUser }
  } catch (error: any) {
    console.error(`❌ Error signing in with ${providerName}:`, error)

    // Handle specific error cases
    if (error.code === 'auth/cancelled-popup-request') {
      return { success: false, error: 'Sign-in was cancelled. Please try again.' }
    } else if (error.code === 'auth/popup-blocked') {
      return { success: false, error: 'Popup was blocked by browser. Please allow popups and try again.' }
    } else if (error.code === 'auth/popup-closed-by-user') {
      return { success: false, error: 'Sign-in was cancelled by user.' }
    } else if (error.code === 'auth/account-exists-with-different-credential') {
      return { success: false, error: 'An account already exists with the same email address but different sign-in credentials.' }
    }

    return { success: false, error: error.message }
  }
}

// Sign in with Google
export async function signInWithGoogle(): Promise<{ success: boolean; user?: AuthUser; error?: string }> {
  // Configure Google provider with additional settings
  googleProvider.setCustomParameters({
    prompt: 'select_account'
  })

  return signInWithProvider(googleProvider, 'Google')
}

// Sign in with Facebook
export async function signInWithFacebook(): Promise<{ success: boolean; user?: AuthUser; error?: string }> {
  // Configure Facebook provider
  facebookProvider.addScope('email')
  facebookProvider.setCustomParameters({
    display: 'popup'
  })

  return signInWithProvider(facebookProvider, 'Facebook')
}

// Sign in with Apple
export async function signInWithApple(): Promise<{ success: boolean; user?: AuthUser; error?: string }> {
  // Configure Apple provider
  appleProvider.addScope('email')
  appleProvider.addScope('name')

  return signInWithProvider(appleProvider, 'Apple')
}

// Sign out
export async function signOut(): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🚪 Starting sign out...')
    await firebaseSignOut(auth)
    console.log('✅ User signed out successfully')
    return { success: true }
  } catch (error: any) {
    console.error('❌ Error signing out:', error)
    return { success: false, error: error.message }
  }
}

// Get current user
export function getCurrentUser(): Promise<User | null> {
  return new Promise((resolve) => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      unsubscribe()
      resolve(user)
    })
  })
}

// Auth state listener
export function onAuthStateChange(callback: (user: User | null) => void) {
  return onAuthStateChanged(auth, callback)
}
