// Simple translation hook for now
// TODO: Implement proper i18n when needed

export type SupportedLocale = 'en' | 'zh-TW' | 'zh-CN'

export function getAvailableLocales(): SupportedLocale[] {
  return ['en', 'zh-TW', 'zh-CN']
}

export function getLocaleName(locale: SupportedLocale): string {
  const names: Record<SupportedLocale, string> = {
    'en': 'English',
    'zh-TW': '繁體中文',
    'zh-CN': '简体中文'
  }
  return names[locale]
}

export function useTranslation() {
  const t = (key: string) => {
    // For now, just return the key as-is
    // In the future, this can be replaced with proper translation logic
    const translations: Record<string, string> = {
      'upload.dragDrop': 'Drag and drop files here, or click to select',
      'upload.selectFiles': 'Select Files',
      'upload.supportedFormats': 'Supported formats',
      'upload.maxSize': 'Max file size',
      'upload.processing': 'Processing...',
      'upload.error': 'Error',
      'upload.success': 'Success',
      'upload.remove': 'Remove',
      'upload.retry': 'Retry',
    }
    
    return translations[key] || key
  }

  return { t }
}
