import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { stripe, SUBSCRIPTION_PLANS } from '@/lib/stripe'
// Note: Database operations removed - this is a web-only repository
import { Timestamp } from 'firebase/firestore'
import Stripe from 'stripe'
import { firebaseApi } from '@/lib/firebase-api'
import { signInWithCustomToken } from 'firebase/auth'
import { auth } from '@/lib/firebase'

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const headersList = await headers()
    const signature = headersList.get('stripe-signature')

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing stripe signature' },
        { status: 400 }
      )
    }

    if (!stripe) {
      return NextResponse.json(
        { error: 'Stripe not initialized' },
        { status: 500 }
      )
    }

    let event: Stripe.Event

    try {
      event = stripe.webhooks.constructEvent(
        body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      )
    } catch (err) {
      console.error('Webhook signature verification failed:', err)
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      )
    }

    console.log('Received Stripe webhook event:', event.type)

    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session
        console.log('Checkout session completed:', session.id)

        // Handle successful subscription creation
        if (session.mode === 'subscription' && session.subscription) {
          const subscriptionId = session.subscription as string
          const customerEmail = session.customer_email || session.customer_details?.email

          if (!customerEmail) {
            console.error('No customer email found in session')
            break
          }

          try {
            // Get subscription details
            const subscription = await stripe.subscriptions.retrieve(subscriptionId)
            const priceId = subscription.items.data[0]?.price.id

            if (!priceId) {
              console.error('No price ID found in subscription')
              break
            }

            // Map price ID to plan
            const planId = getPlanFromPriceId(priceId)
            if (!planId) {
              console.error('Unknown price ID:', priceId)
              break
            }

            // Update user subscription in database
            const user = await getUserByEmail(customerEmail)
            if (user) {
              await updateUserSubscription(user.id, {
                subscriptionPlan: planId,
                subscriptionStatus: subscription.status,
                stripeCustomerId: subscription.customer as string,
                stripeSubscriptionId: subscriptionId,
                currentPeriodStart: new Date((subscription as any).current_period_start * 1000),
                currentPeriodEnd: new Date((subscription as any).current_period_end * 1000),
              })
              console.log(`✅ Updated subscription for user ${user.id} to ${planId}`)
            } else {
              console.error('User not found for email:', customerEmail)
            }
          } catch (error) {
            console.error('Failed to update user subscription:', error)
          }
        }
        break
      }

      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice
        console.log('Invoice payment succeeded:', invoice.id)

        try {
          // Get customer email
          const customer = await stripe.customers.retrieve(invoice.customer as string)
          if (!customer || customer.deleted) {
            console.error('Customer not found or deleted')
            break
          }

          const customerEmail = (customer as any).email
          if (!customerEmail) {
            console.error('Customer email not found')
            break
          }

          // Update user subscription status to active using Firebase client SDK
          const user = await getUserByEmail(customerEmail)
          if (user && user.id) {
            await updateUser(user.id, {
              subscriptionStatus: 'active',
              subscriptionEndDate: invoice.period_end ? Timestamp.fromDate(new Date(invoice.period_end * 1000)) : undefined
            })
            console.log('✅ Updated subscription status to active for user:', customerEmail)
          }
        } catch (error) {
          console.error('Failed to handle invoice payment succeeded:', error)
        }
        break
      }

      case 'customer.subscription.created':
      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription
        const eventType = event.type === 'customer.subscription.created' ? 'created' : 'updated'
        console.log(`Subscription ${eventType}:`, subscription.id)

        try {
          // Get customer email
          const customer = await stripe.customers.retrieve(subscription.customer as string)
          if (!customer || customer.deleted) {
            console.error('Customer not found or deleted')
            break
          }

          const customerEmail = (customer as any).email
          if (!customerEmail) {
            console.error('No email found for customer')
            break
          }

          const priceId = subscription.items.data[0]?.price.id
          const planId = getPlanFromPriceId(priceId)

          if (!planId) {
            console.error('Unknown price ID:', priceId)
            break
          }

          // Update user subscription using Firebase client SDK
          const user = await getUserByEmail(customerEmail)
          if (user) {
            // Call create_subscription API for new subscriptions
            if (eventType === 'created') {
              await callCreateSubscriptionAPI(customerEmail, subscription, planId)
            } else {
              // For updates, use Firebase client SDK
              await updateUserSubscription(user.id, {
                subscriptionPlan: planId,
                subscriptionStatus: subscription.status,
                stripeCustomerId: subscription.customer as string,
                stripeSubscriptionId: subscription.id,
                currentPeriodStart: new Date((subscription as any).current_period_start * 1000),
                currentPeriodEnd: new Date((subscription as any).current_period_end * 1000),
              })
            }
            console.log(`✅ ${eventType === 'created' ? 'Created' : 'Updated'} subscription for user ${user.id} - Plan: ${planId}`)
          }
        } catch (error) {
          console.error(`Failed to handle subscription ${eventType}:`, error)
        }
        break
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription
        console.log('Subscription cancelled:', subscription.id)

        try {
          // Get customer email
          const customer = await stripe.customers.retrieve(subscription.customer as string)
          if (!customer || customer.deleted) {
            console.error('Customer not found or deleted')
            break
          }

          const customerEmail = (customer as any).email
          if (!customerEmail) {
            console.error('No email found for customer')
            break
          }

          // Downgrade user to free plan using Firebase client SDK
          const user = await getUserByEmail(customerEmail)
          if (user) {
            await updateUserSubscription(user.id, {
              subscriptionPlan: 'FREE',
              subscriptionStatus: 'cancelled',
              stripeSubscriptionId: null,
              currentPeriodStart: null,
              currentPeriodEnd: null,
            })
            console.log(`✅ Downgraded user ${user.id} to FREE plan`)
          }
        } catch (error) {
          console.error('Failed to handle subscription cancellation:', error)
        }
        break
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice
        console.log('❌ Payment failed for invoice:', invoice.id)
        // Handle failed payment - could send email notification
        break
      }

      // Silently handle these common events that don't require action
      case 'customer.created':
      case 'customer.updated':
      case 'payment_method.attached':
      case 'charge.succeeded':
      case 'payment_intent.created':
      case 'payment_intent.succeeded':
      case 'invoice.created':
      case 'invoice.finalized':
      case 'invoice.paid':
        // These events are handled automatically by Stripe or don't require action
        break

      default:
        // Only log truly unexpected events
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('Webhook error:', error)
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    )
  }
}

// Helper function to map Stripe price IDs to plan names
function getPlanFromPriceId(priceId: string): string | null {
  // Find the plan that matches this price ID
  for (const [planKey, planConfig] of Object.entries(SUBSCRIPTION_PLANS)) {
    if (planKey === 'FREE') continue // Skip FREE plan as it has no price IDs

    // Type assertion since we know non-FREE plans have price IDs
    const paidPlan = planConfig as any
    if (paidPlan.monthlyPriceId === priceId || paidPlan.yearlyPriceId === priceId) {
      return planKey
    }
  }
  return null
}

// Helper function to call create_subscription API using Firebase client SDK
async function callCreateSubscriptionAPI(customerEmail: string, subscription: any, planId: string) {
  try {
    // First get user from database to get user ID
    const user = await getUserByEmail(customerEmail)
    if (!user) {
      console.error('User not found for email:', customerEmail)
      return
    }

    const requestData = {
      // Stripe payment information (required by CreateSubscriptionRequest)
      stripe_customer_id: subscription.customer,
      stripe_subscription_id: subscription.id,
      plan_id: planId,
      status: subscription.status,
      current_period_start: subscription.current_period_start ? new Date(subscription.current_period_start * 1000).toISOString() : new Date().toISOString(),
      current_period_end: subscription.current_period_end ? new Date(subscription.current_period_end * 1000).toISOString() : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      amount_paid: subscription.items.data[0]?.price.unit_amount || 0,
      payment_status: 'succeeded',
      period_start: subscription.current_period_start ? new Date(subscription.current_period_start * 1000).toISOString() : new Date().toISOString(),
      period_end: subscription.current_period_end ? new Date(subscription.current_period_end * 1000).toISOString() : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    }

    console.log('🔄 Using Firebase client SDK for create_subscription (Webhook context)...')
    console.log('📤 Firebase callable function call with data:', JSON.stringify(requestData, null, 2))

    try {
      // Create a custom token for the user and sign in
      // Note: This requires Firebase Admin SDK to create custom token
      // For now, we'll use the Firebase client SDK directly without authentication
      // The backend function should handle webhook calls differently

      const result = await firebaseApi.createSubscription(requestData)
      console.log('✅ Successfully called create_subscription (Firebase client SDK):', result)
      return result
    } catch (authError) {
      console.warn('⚠️ Firebase client SDK failed (expected in webhook context), using fallback:', authError)

      // Fallback to local database update
      await updateUserSubscription(user.id, {
        subscriptionPlan: planId,
        subscriptionStatus: subscription.status,
        stripeCustomerId: subscription.customer as string,
        stripeSubscriptionId: subscription.id,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
      })
      console.log('✅ Used fallback database update for user:', user.id)
    }
  } catch (error) {
    console.error('❌ Failed to call create_subscription:', error)
  }
}
