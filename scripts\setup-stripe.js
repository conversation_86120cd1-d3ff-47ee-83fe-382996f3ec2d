#!/usr/bin/env node

/**
 * Stripe Setup Script for SpeakOneAI
 *
 * This script creates products and prices in Stripe based on the subscription plans
 * defined in Subscription.md
 *
 * Usage: node scripts/setup-stripe.js
 */

const Stripe = require('stripe');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// Subscription plans based on Subscription.md
const SUBSCRIPTION_PLANS = {
  STARTER: {
    name: 'Starter',
    description: '1 hour per day, client apps only (Windows, MacOS), Maximum device limit: 1',
    monthlyPrice: 999, // $9.99
    yearlyPrice: 9900, // $99.00
    features: [
      '1 hour per day (30 hours per month)',
      'Access to client app only (Windows, MacOS)',
      'Maximum device limit: 1'
    ]
  },
  PRO: {
    name: 'Pro',
    description: '2 hours per day, all platforms, Maximum device limit: 2',
    monthlyPrice: 1999, // $19.99
    yearlyPrice: 19900, // $199.00
    features: [
      '2 hours per day (60 hours per month)',
      'Access to all platforms (Windows, MacOS, iOS, Android)',
      'Maximum device limit: 2'
    ]
  },
  PREMIUM: {
    name: 'Premium',
    description: '8 hours per day, all platforms, Maximum device limit: 5',
    monthlyPrice: 5999, // $59.99
    yearlyPrice: 55900, // $559.00
    features: [
      '8 hours per day (240 hours per month)',
      'Access to all platforms (Windows, MacOS, iOS, Android)',
      'Maximum device limit: 5'
    ]
  },
  MAX: {
    name: 'Max',
    description: 'Unlimited usage, all platforms, Unlimited devices',
    monthlyPrice: 12999, // $129.99
    yearlyPrice: 109900, // $1099.00
    features: [
      'Unlimited hours per month',
      'Access to all platforms (Windows, MacOS, iOS, Android)',
      'Unlimited device limit'
    ]
  }
};

// Function to update .env.local file
async function updateEnvFile(newEnvVars) {
  const envPath = path.join(__dirname, '..', '.env.local');

  try {
    let envContent = '';

    // Read existing .env.local file
    if (fs.existsSync(envPath)) {
      envContent = fs.readFileSync(envPath, 'utf8');
    }

    // Remove existing Stripe price IDs
    const lines = envContent.split('\n');
    const filteredLines = lines.filter(line =>
      !line.startsWith('STRIPE_PRICE_ID_STARTER') &&
      !line.startsWith('STRIPE_PRICE_ID_PRO') &&
      !line.startsWith('STRIPE_PRICE_ID_PREMIUM') &&
      !line.startsWith('STRIPE_PRICE_ID_MAX')
    );

    // Add new environment variables
    const updatedContent = filteredLines.join('\n') + '\n\n# Stripe Subscription Price IDs (Generated by setup-stripe.js)\n' + newEnvVars.join('\n') + '\n';

    // Write back to file
    fs.writeFileSync(envPath, updatedContent);
    console.log('✅ .env.local file updated successfully!');

  } catch (error) {
    console.error('❌ Error updating .env.local file:', error.message);
    console.log('Please manually add the environment variables shown above to your .env.local file.');
  }
}

async function createStripeProducts() {
  console.log('🚀 Setting up Stripe products and prices for SpeechPilot...\n');

  const results = {};

  try {
    for (const [planKey, planConfig] of Object.entries(SUBSCRIPTION_PLANS)) {
      console.log(`📦 Creating product: ${planConfig.name}`);

      // Create product
      const product = await stripe.products.create({
        name: planConfig.name,
        description: planConfig.description,
        metadata: {
          plan: planKey.toLowerCase(),
          features: JSON.stringify(planConfig.features)
        }
      });

      console.log(`✅ Product created: ${product.id}`);

      // Create monthly price
      console.log(`💰 Creating monthly price: $${(planConfig.monthlyPrice / 100).toFixed(2)}/month`);

      const monthlyPrice = await stripe.prices.create({
        product: product.id,
        unit_amount: planConfig.monthlyPrice,
        currency: 'usd',
        recurring: {
          interval: 'month'
        },
        metadata: {
          plan: planKey.toLowerCase(),
          billing_period: 'monthly'
        }
      });

      console.log(`✅ Monthly price created: ${monthlyPrice.id}`);

      // Create yearly price
      console.log(`💰 Creating yearly price: $${(planConfig.yearlyPrice / 100).toFixed(2)}/year`);

      const yearlyPrice = await stripe.prices.create({
        product: product.id,
        unit_amount: planConfig.yearlyPrice,
        currency: 'usd',
        recurring: {
          interval: 'year'
        },
        metadata: {
          plan: planKey.toLowerCase(),
          billing_period: 'yearly'
        }
      });

      console.log(`✅ Yearly price created: ${yearlyPrice.id}`);

      results[planKey] = {
        productId: product.id,
        monthlyPriceId: monthlyPrice.id,
        yearlyPriceId: yearlyPrice.id,
        name: planConfig.name,
        monthlyPrice: planConfig.monthlyPrice,
        yearlyPrice: planConfig.yearlyPrice
      };

      console.log('');
    }

    // Generate environment variables
    console.log('📝 Generated Environment Variables:');
    console.log('These will be automatically added to your .env.local file:\n');

    const envVars = [];
    for (const [planKey, result] of Object.entries(results)) {
      envVars.push(`STRIPE_PRICE_ID_${planKey}_MONTHLY="${result.monthlyPriceId}"`);
      envVars.push(`STRIPE_PRICE_ID_${planKey}_YEARLY="${result.yearlyPriceId}"`);
      console.log(`STRIPE_PRICE_ID_${planKey}_MONTHLY="${result.monthlyPriceId}"`);
      console.log(`STRIPE_PRICE_ID_${planKey}_YEARLY="${result.yearlyPriceId}"`);
    }

    // Update .env.local file
    await updateEnvFile(envVars);

    console.log('\n🎉 Stripe setup completed successfully!');
    console.log('\n📋 Summary:');

    for (const [planKey, result] of Object.entries(results)) {
      console.log(`${planKey}: ${result.name}`);
      console.log(`  Monthly: $${(result.monthlyPrice / 100).toFixed(2)}/month`);
      console.log(`  Yearly: $${(result.yearlyPrice / 100).toFixed(2)}/year`);
    }

    // Save results to file
    const outputPath = path.join(__dirname, 'stripe-setup-results.json');
    fs.writeFileSync(outputPath, JSON.stringify(results, null, 2));
    console.log(`\n💾 Results saved to: ${outputPath}`);

  } catch (error) {
    console.error('❌ Error setting up Stripe:', error.message);
    process.exit(1);
  }
}

// Validation function
function validateEnvironment() {
  if (!process.env.STRIPE_SECRET_KEY) {
    console.error('❌ STRIPE_SECRET_KEY not found in .env.local');
    console.log('Please add your Stripe secret key to .env.local:');
    console.log('STRIPE_SECRET_KEY="sk_test_..."');
    process.exit(1);
  }

  if (!process.env.STRIPE_SECRET_KEY.startsWith('sk_')) {
    console.error('❌ Invalid Stripe secret key format');
    process.exit(1);
  }

  console.log('✅ Environment validation passed');
}

// Main execution
async function main() {
  console.log('🔧 SpeakOneAI Stripe Setup Script\n');

  validateEnvironment();
  await createStripeProducts();
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { createStripeProducts, SUBSCRIPTION_PLANS };
