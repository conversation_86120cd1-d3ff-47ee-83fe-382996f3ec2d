rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidUser() {
      return isAuthenticated() &&
             (request.auth.token.email_verified == true ||
              request.auth.token.firebase.sign_in_provider == 'google.com');
    }
    
    // Users collection
    match /users/{userId} {
      // Users can read and write their own data
      allow read, write: if isOwner(userId) && isValidUser();

      // Allow creation during registration (Firebase Auth will handle this)
      allow create: if isAuthenticated() &&
                   request.auth.uid == userId &&
                   (request.auth.token.email_verified == true ||
                    request.auth.token.firebase.sign_in_provider == 'google.com');
    }

    
    // Purchases collection
    match /purchases/{purchaseId} {
      // Users can read their own purchases
      allow read: if isAuthenticated() && 
                 resource.data.userId == request.auth.uid;
      
      // Only server (via Admin SDK) can create/update purchases
      // This happens via Stripe webhooks and checkout API
      allow create, update: if false; // Handled by server-side functions
    }
    
    // Usage logs collection
    match /usage_logs/{logId} {
      // Users can read their own usage logs
      allow read: if isAuthenticated() && 
                 resource.data.userId == request.auth.uid;
      
      // Users can create usage logs (from client apps)
      allow create: if isAuthenticated() && 
                   request.auth.uid == resource.data.userId &&
                   isValidUsageLog();
      
      // Only allow updates to end session
      allow update: if isAuthenticated() && 
                   request.auth.uid == resource.data.userId &&
                   isValidUsageUpdate();
    }
    
    // Validation functions for usage logs
    function isValidUsageLog() {
      let data = request.resource.data;
      return data.keys().hasAll(['userId', 'sessionStart', 'platform', 'createdAt']) &&
             data.userId == request.auth.uid &&
             data.platform in ['windows', 'macos'] &&
             data.sessionStart is timestamp &&
             data.createdAt is timestamp;
    }
    
    function isValidUsageUpdate() {
      let data = request.resource.data;
      let existing = resource.data;

      // Only allow updating sessionEnd and secondsUsed
      return data.diff(existing).affectedKeys().hasOnly(['sessionEnd', 'secondsUsed']) &&
             data.sessionEnd is timestamp &&
             data.secondsUsed is number &&
             data.secondsUsed >= 0;
    }
    
    // Deny all other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
