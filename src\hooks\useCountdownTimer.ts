import { useState, useEffect, useCallback } from 'react'

interface CountdownTime {
  hours: number
  minutes: number
  seconds: number
  totalSeconds: number
}

interface UseCountdownTimerProps {
  resetTime?: Date | string
  onReset?: () => void
}

export const useCountdownTimer = ({ resetTime, onReset }: UseCountdownTimerProps = {}) => {
  const [timeLeft, setTimeLeft] = useState<CountdownTime>({
    hours: 0,
    minutes: 0,
    seconds: 0,
    totalSeconds: 0
  })

  const calculateTimeLeft = useCallback(() => {
    const now = new Date()
    let reset: Date

    // Handle different resetTime formats
    if (resetTime instanceof Date) {
      reset = resetTime
    } else if (typeof resetTime === 'string') {
      reset = new Date(resetTime)
    } else {
      // Default to next midnight if resetTime is not provided or invalid
      reset = new Date()
      reset.setHours(24, 0, 0, 0)
    }

    // If reset time is invalid or in the past, set to next midnight
    if (isNaN(reset.getTime()) || reset <= now) {
      reset = new Date()
      reset.setHours(24, 0, 0, 0)
    }

    const difference = reset.getTime() - now.getTime()
    
    if (difference <= 0) {
      // Time has reached zero, trigger reset
      if (onReset) {
        onReset()
      }
      
      // Reset to 24 hours
      const nextReset = new Date()
      nextReset.setHours(24, 0, 0, 0)
      const nextDifference = nextReset.getTime() - now.getTime()
      
      const hours = Math.floor(nextDifference / (1000 * 60 * 60))
      const minutes = Math.floor((nextDifference % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((nextDifference % (1000 * 60)) / 1000)
      
      return {
        hours,
        minutes,
        seconds,
        totalSeconds: Math.floor(nextDifference / 1000)
      }
    }

    const hours = Math.floor(difference / (1000 * 60 * 60))
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((difference % (1000 * 60)) / 1000)

    return {
      hours,
      minutes,
      seconds,
      totalSeconds: Math.floor(difference / 1000)
    }
  }, [resetTime, onReset])

  useEffect(() => {
    // Calculate initial time
    setTimeLeft(calculateTimeLeft())

    // Set up interval to update every second
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft())
    }, 1000)

    return () => clearInterval(timer)
  }, [calculateTimeLeft])

  const formatTime = useCallback((time: CountdownTime) => {
    const { hours, minutes } = time
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }, [])

  const formatTimeWithSeconds = useCallback((time: CountdownTime) => {
    const { hours, minutes, seconds } = time
    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`
    }
    return `${minutes}m ${seconds}s`
  }, [])

  return {
    timeLeft,
    formatTime,
    formatTimeWithSeconds,
    isExpired: timeLeft.totalSeconds <= 0
  }
}
