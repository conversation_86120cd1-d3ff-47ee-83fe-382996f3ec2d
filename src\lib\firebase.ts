// Firebase configuration with multi-environment support
import { initializeApp, getApps } from 'firebase/app'
import { getAnalytics } from "firebase/analytics";
import { getFirestore } from 'firebase/firestore'
import { getAuth } from 'firebase/auth'

// Get current environment
const getEnvironment = () => {
  return process.env.NEXT_PUBLIC_APP_ENV || process.env.NODE_ENV || 'development'
}

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.GOOGLE_ANALYTICS_MEASUREMENT_ID,
}

// Validate Firebase configuration
const validateFirebaseConfig = () => {
  const requiredFields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId']
  const missingFields = requiredFields.filter(field => !firebaseConfig[field as keyof typeof firebaseConfig])

  if (missingFields.length > 0) {
    console.warn(`⚠️ Missing Firebase configuration fields: ${missingFields.join(', ')}`)
    console.warn(`Current environment: ${getEnvironment()}`)
    console.warn(`Project ID: ${firebaseConfig.projectId}`)
  }

  return missingFields.length === 0
}

// Initialize Firebase
let app: any = null
let db: any = null
let auth: any = null
let analytics: any = null

// Function to ensure Firebase is initialized
function ensureFirebaseInitialized() {
  if (app && db && auth) {
    return { app, db, auth, analytics }
  }

  try {
    // Validate configuration before initializing
    if (validateFirebaseConfig()) {
      app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0]

      // Initialize Firestore
      db = getFirestore(app)

      // Initialize Auth
      auth = getAuth(app)

      // Initialize Analytics (only in browser)
      analytics = typeof window !== 'undefined' ? getAnalytics(app) : null

      console.log(`✅ Firebase initialized successfully for environment: ${getEnvironment()}`)
      console.log(`🔥 Project ID: ${firebaseConfig.projectId}`)
    } else {
      console.error('❌ Firebase configuration is incomplete')
      throw new Error('Firebase configuration is incomplete')
    }
  } catch (error) {
    console.error('❌ Failed to initialize Firebase:', error)
    throw error
  }

  return { app, db, auth, analytics }
}

// Initialize Firebase immediately
try {
  ensureFirebaseInitialized()
} catch (error) {
  console.error('Initial Firebase initialization failed:', error)
}

// Export Firebase services with lazy initialization
export function getFirebaseServices() {
  return ensureFirebaseInitialized()
}

export { db, auth, analytics }
export default app

// Export environment utilities
export const getCurrentEnvironment = getEnvironment
export const isProduction = () => getEnvironment() === 'production'
export const isDevelopment = () => getEnvironment() === 'development'
