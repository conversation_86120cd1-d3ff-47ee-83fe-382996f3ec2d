'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { useLanguage } from '@/contexts/language-context'
import {
  SupportedLocale,
  getAvailableLocales
} from '@/hooks/use-translation'
import {
  getLanguageDisplayName,
  getLanguageFlag
} from '@/lib/language-detection'
import { ChevronDown, Globe } from 'lucide-react'

interface LanguageSwitcherProps {
  variant?: 'button' | 'dropdown' | 'minimal'
  className?: string
  showFlag?: boolean
  showText?: boolean
}

export function LanguageSwitcher({
  variant = 'dropdown',
  className = '',
  showFlag = true,
  showText = true
}: LanguageSwitcherProps) {
  const { locale: currentLocale, setLocale } = useLanguage()
  const [isOpen, setIsOpen] = useState(false)
  const availableLocales = getAvailableLocales()

  const handleLanguageChange = async (newLocale: SupportedLocale) => {
    if (newLocale === currentLocale) {
      setIsOpen(false)
      return
    }

    // Update locale through context
    setLocale(newLocale)
    setIsOpen(false)
  }

  if (variant === 'minimal') {
    return (
      <div className={`relative ${className}`}>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsOpen(!isOpen)}
          className="h-8 w-8 p-0"
          aria-label="Change language"
        >
          <Globe className="h-4 w-4" />
        </Button>
        
        {isOpen && (
          <>
            {/* Backdrop */}
            <div 
              className="fixed inset-0 z-40" 
              onClick={() => setIsOpen(false)}
            />
            
            {/* Dropdown */}
            <div className="absolute right-0 top-full mt-1 z-50 min-w-[120px] bg-white border border-gray-200 rounded-md shadow-lg">
              {availableLocales.map((locale) => (
                <button
                  key={locale}
                  onClick={() => handleLanguageChange(locale)}
                  className={`w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2 ${
                    locale === currentLocale ? 'bg-purple-50 text-purple-600' : 'text-gray-700'
                  }`}
                >
                  {showFlag && (
                    <span className="text-base">{getLanguageFlag(locale)}</span>
                  )}
                  {showText && getLanguageDisplayName(locale)}
                </button>
              ))}
            </div>
          </>
        )}
      </div>
    )
  }

  if (variant === 'button') {
    return (
      <div className={`flex gap-1 ${className}`}>
        {availableLocales.map((locale) => (
          <Button
            key={locale}
            variant={locale === currentLocale ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleLanguageChange(locale)}
            className="h-8 px-2"
          >
            {showFlag && (
              <span className="text-sm mr-1">{getLanguageFlag(locale)}</span>
            )}
            {showText && (
              <span className="text-xs">{getLanguageDisplayName(locale)}</span>
            )}
          </Button>
        ))}
      </div>
    )
  }

  // Default dropdown variant
  return (
    <div className={`relative ${className}`}>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="h-8 px-3 flex items-center gap-2"
        aria-label="Change language"
      >
        {showFlag && (
          <span className="text-sm">{getLanguageFlag(currentLocale)}</span>
        )}
        {showText && (
          <span className="text-sm">{getLanguageDisplayName(currentLocale)}</span>
        )}
        <ChevronDown className="h-3 w-3" />
      </Button>
      
      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 top-full mt-1 z-50 min-w-[140px] bg-white border border-gray-200 rounded-md shadow-lg">
            {availableLocales.map((locale) => (
              <button
                key={locale}
                onClick={() => handleLanguageChange(locale)}
                className={`w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2 first:rounded-t-md last:rounded-b-md ${
                  locale === currentLocale ? 'bg-purple-50 text-purple-600' : 'text-gray-700'
                }`}
              >
                {showFlag && (
                  <span className="text-base">{getLanguageFlag(locale)}</span>
                )}
                {showText && (
                  <span>{getLanguageDisplayName(locale)}</span>
                )}
                {locale === currentLocale && (
                  <span className="ml-auto text-purple-600">✓</span>
                )}
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  )
}
