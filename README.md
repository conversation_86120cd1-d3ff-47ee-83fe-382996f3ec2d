# SpeakOneAI Web

A modern web application for purchasing and downloading SpeakOneAI speech-to-text software for Windows and macOS. Built with Next.js, Firebase, and Stripe for a seamless payment and download experience.

firebase deploy --only hosting --project speakoneai-dev-9f995

## 🚀 Features

- **🔐 SSO Authentication** - Secure login with Google, Facebook, Apple, no email registration
- **💳 One-time Purchases** - Buy Windows/macOS apps or bundle via Stripe
- **📊 Usage Tracking** - Monitor client app usage hours across all platforms
- **� Cross-platform Support** - Windows, macOS, iOS, and Android client apps
- **🎨 Modern UI** - Responsive design with Tailwind CSS and shadcn/ui
- **� Secure Database** - Firestore with strict security rules
- **📈 Analytics Ready** - Google Analytics integration for user behavior tracking
- **� Scalable Architecture** - Firebase Functions for server-side operations

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Authentication**: NextAuth.js with Google OAuth
- **Database**: Firebase/Firestore with security rules
- **Payments**: Stripe (Subscription purchases)
- **Functions**: Firebase Functions for webhooks and usage tracking
- **UI Components**: shadcn/ui, Radix UI
- **Analytics**: Google Analytics
- **Deployment**: Vercel (recommended)


## SpeakOneAI Ecosystem
The client app and mobile will have the core AI features for user to use AI speech-to-text or direct speech-to-text. User will login their client app or mobile app with SSO to authenticate with their SpeakOneAI account. AI features usage will be tracked and recorded and update to the firestore via Firebase Functions. Also, user can manage their account, subscription, and usage hours (in second level) in the web app.
- Client APP - SpeakOneAI Windows & MacOS
- Mobile APP - SpeakOneAI iOS & Android


## 📋 Prerequisites

- Node.js 18+- Stripe account
- Git
