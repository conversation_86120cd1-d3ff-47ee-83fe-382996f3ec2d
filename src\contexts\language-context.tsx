'use client'

import React, { createContext, useContext, useState } from 'react'

export type SupportedLocale = 'en' | 'zh-TW' | 'zh-CN'

interface LanguageContextType {
  locale: SupportedLocale
  setLocale: (locale: SupportedLocale) => void
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [locale, setLocale] = useState<SupportedLocale>('en')

  return (
    <LanguageContext.Provider value={{ locale, setLocale }}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

export function getAvailableLocales(): SupportedLocale[] {
  return ['en', 'zh-TW', 'zh-CN']
}

export function getLocaleName(locale: SupportedLocale): string {
  const names: Record<SupportedLocale, string> = {
    'en': 'English',
    'zh-TW': '繁體中文',
    'zh-CN': '简体中文'
  }
  return names[locale]
}
