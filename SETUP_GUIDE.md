# SpeechPilot 設定指南

## 🔧 必要設定步驟

### 1. Google OAuth 設定

1. 前往 [Google Cloud Console](https://console.cloud.google.com/)
2. 選擇專案 `speechpilot-f1495`
3. 前往 "APIs & Services" > "Credentials"
4. 點擊 "Create Credentials" > "OAuth 2.0 Client IDs"
5. 選擇 "Web application"
6. 設定授權的重定向 URI：
   - 開發環境：`http://localhost:3000/api/auth/callback/google`
   - 生產環境：`https://yourdomain.com/api/auth/callback/google`

7. 複製 Client ID 和 Client Secret 到 `.env.local`：
```env
GOOGLE_CLIENT_ID="your-client-id.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="your-client-secret"
```

### 2. Firebase Admin SDK 設定

1. 前往 [Firebase Console](https://console.firebase.google.com/)
2. 選擇專案 `speechpilot-f1495`
3. 前往 Project Settings > Service Accounts
4. 點擊 "Generate new private key"
5. 下載 JSON 檔案
6. 從 JSON 檔案中複製以下資訊到 `.env.local`：

```env
FIREBASE_PROJECT_ID="speechpilot-f1495"
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n你的私鑰內容\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL="<EMAIL>"
```

**重要提醒**：
- 私鑰中的 `\n` 需要保持為字面字符串，不要替換為實際換行
- 確保私鑰包含完整的 BEGIN 和 END 標記

### 3. 啟動應用程式

完成上述設定後：

```bash
npm run dev
```

然後訪問 `http://localhost:3000` 並測試 Google 登入功能。

## 🚨 常見問題

### "Invalid PEM formatted message" 錯誤
- 檢查 `FIREBASE_PRIVATE_KEY` 是否正確設定
- 確保私鑰包含完整的 BEGIN 和 END 標記
- 確保 `\n` 字符沒有被替換為實際換行

### Google OAuth 錯誤
- 檢查 `GOOGLE_CLIENT_ID` 和 `GOOGLE_CLIENT_SECRET` 是否正確
- 確保重定向 URI 在 Google Cloud Console 中已正確設定

### Firebase 連接錯誤
- 檢查 `FIREBASE_PROJECT_ID` 是否與你的 Firebase 專案 ID 一致
- 確保 Service Account 有適當的權限

## 📝 當前狀態

✅ Firebase 客戶端配置已完成
⏳ 需要設定 Google OAuth 憑證
⏳ 需要設定 Firebase Admin SDK 憑證

完成這些設定後，你的 Google 登入功能就能正常運作了。
