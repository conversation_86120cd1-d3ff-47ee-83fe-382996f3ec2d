# SpeakOneAI 開發指南

## 🚀 快速開始

### 1. 環境設置

```bash
# 克隆專案
git clone <your-repo-url>
cd SpeakOneAIWeb

# 安裝依賴
npm install

# 設置開發環境
npm run env:dev
```

### 2. 配置環境變數

複製並編輯開發環境配置：
```bash
cp .env.development .env.development.local
# 編輯 .env.development.local 並填入你的實際 API 密鑰
```

### 3. 啟動開發服務器

**選項 1: 分別啟動（推薦用於調試）**
```bash
# 終端 1: 啟動 Next.js
npm run dev

# 終端 2: 啟動 Stripe webhook 監聽器
npm run stripe:listen
```

**選項 2: 同時啟動**
```bash
# 需要先安裝 concurrently
npm install -g concurrently

# 同時啟動開發服務器和 Stripe webhook
npm run dev:full
```

## 🔧 開發工具

### Stripe CLI 設置

1. **安裝 Stripe CLI**
   ```bash
   # macOS
   brew install stripe/stripe-cli/stripe
   
   # Windows
   scoop bucket add stripe https://github.com/stripe/scoop-stripe-cli.git
   scoop install stripe
   ```

2. **登入並測試**
   ```bash
   stripe login
   stripe listen --forward-to localhost:3000/api/webhooks/stripe
   ```

3. **獲取 webhook 密鑰**
   - 運行 `stripe listen` 後會顯示 webhook 簽名密鑰
   - 將其添加到 `.env.local` 的 `STRIPE_WEBHOOK_SECRET`

### Firebase 設置

1. **安裝 Firebase CLI**
   ```bash
   npm install -g firebase-tools
   firebase login
   ```

2. **本地模擬器**
   ```bash
   # 啟動 Firestore 模擬器
   firebase emulators:start --only firestore
   
   # 啟動 Functions 模擬器
   firebase emulators:start --only functions
   ```

## 📝 開發腳本

```bash
# 開發相關
npm run dev              # 啟動開發服務器
npm run dev:full         # 同時啟動開發服務器和 Stripe webhook
npm run build            # 構建應用
npm run start            # 啟動生產服務器

# 測試相關
npm run test             # 運行測試
npm run test:watch       # 監視模式運行測試
npm run test:coverage    # 運行測試並生成覆蓋率報告

# Stripe 相關
npm run stripe:listen    # 啟動 Stripe webhook 監聽器
npm run setup-stripe     # 設置 Stripe 產品和價格

# 環境管理
npm run env:dev          # 切換到開發環境
npm run env:prod         # 切換到生產環境

# 部署相關
npm run deploy:dev       # 部署到開發環境
npm run deploy:prod      # 部署到生產環境
```

## 🐛 常見問題

### 1. Stripe webhook 不工作
- 確保 Stripe CLI 正在運行
- 檢查 `STRIPE_WEBHOOK_SECRET` 是否正確
- 確認 webhook 端點 URL 正確

### 2. Firebase 連接問題
- 檢查 Firebase 配置是否正確
- 確認專案 ID 是否匹配
- 檢查服務帳戶密鑰是否有效

### 3. 環境變數問題
- 確保 `.env.local` 文件存在
- 檢查所有必要的環境變數是否已設置
- 重新啟動開發服務器

## 📚 專案結構

```
src/
├── app/                 # Next.js 13+ App Router
├── components/          # React 組件
├── lib/                # 工具函數和配置
├── hooks/              # 自定義 React Hooks
├── types/              # TypeScript 類型定義
└── contexts/           # React Context

functions/              # Firebase Functions
scripts/               # 開發和部署腳本
docs/                  # 文檔
```

## 🔄 開發工作流程

1. **功能開發**
   - 創建功能分支
   - 本地開發和測試
   - 提交代碼

2. **測試**
   - 運行單元測試
   - 本地端到端測試
   - Stripe 支付流程測試

3. **部署**
   - 部署到開發環境測試
   - 代碼審查
   - 部署到生產環境

## 📞 支援

如果遇到問題，請：
1. 檢查本文檔的常見問題部分
2. 查看 `DEPLOYMENT.md` 獲取詳細部署信息
3. 檢查專案的 Issues 頁面
