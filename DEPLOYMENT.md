# SpeakOneAI 部署指南

本指南說明如何管理開發和生產環境，以及如何使用 Firebase 進行部署。

## 🏗️ 環境配置

### 環境文件結構

```
.env.development    # 開發環境配置
.env.production     # 生產環境配置
.env.local         # 當前使用的環境配置（由部署腳本自動生成）
```

### 設置環境變數

1. **複製並編輯開發環境配置**：
   ```bash
   cp .env.development .env.development.local
   # 編輯 .env.development.local 並填入你的開發環境實際值
   ```

2. **複製並編輯生產環境配置**：
   ```bash
   cp .env.production .env.production.local
   # 編輯 .env.production.local 並填入你的生產環境實際值
   ```

### 必要的環境變數

確保以下變數在你的環境文件中都有正確的值：

#### Firebase 配置
- `NEXT_PUBLIC_FIREBASE_API_KEY`
- `NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN`
- `NEXT_PUBLIC_FIREBASE_PROJECT_ID`
- `FIREBASE_PROJECT_ID`
- `FIREBASE_CLIENT_EMAIL`
- `FIREBASE_PRIVATE_KEY`

#### Stripe 配置
- `STRIPE_SECRET_KEY`
- `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`
- `STRIPE_WEBHOOK_SECRET`
- 所有 Stripe Price IDs

#### 其他配置
- `NEXT_PUBLIC_SITE_URL`
- `NEXTAUTH_URL`
- `NEXTAUTH_SECRET`

## 🚀 部署流程

### 快速部署命令

```bash
# 部署到開發環境
npm run deploy:dev

# 部署到生產環境
npm run deploy:prod
```

### 手動環境切換

```bash
# 切換到開發環境
npm run env:dev

# 切換到生產環境
npm run env:prod
```

### 部署步驟詳解

部署腳本會自動執行以下步驟：

1. **環境驗證** - 檢查必要的環境變數是否已設置
2. **環境配置** - 複製對應的環境文件到 `.env.local`
3. **應用構建** - 執行 `npm run build`
4. **Firebase 部署** - 部署到對應的 Firebase 專案

## 🔥 Firebase 專案設置

### 1. 創建 Firebase 專案

為開發和生產環境分別創建 Firebase 專案：

- 開發環境：`speakoneai-dev`
- 生產環境：`speakoneai-prod`

### 2. 配置 Firebase CLI

```bash
# 安裝 Firebase CLI
npm install -g firebase-tools

# 登入 Firebase
firebase login

# 初始化專案（如果還沒有）
firebase init
```

### 3. 設置專案別名

編輯 `.firebaserc` 文件：

```json
{
  "projects": {
    "development": "speakoneai-dev",
    "production": "speakoneai-prod",
    "default": "speakoneai-dev"
  }
}
```

### 4. Firebase 配置文件

確保 `firebase.json` 配置正確：

```json
{
  "firestore": {
    "rules": "firestore.rules",
    "indexes": "firestore.indexes.json"
  },
  "functions": {
    "source": "functions",
    "codebase": "default",
    "runtime": "nodejs18"
  },
  "hosting": {
    "source": ".",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "frameworksBackend": {
      "region": "asia-east1"
    }
  }
}
```

## 🛠️ 開發工作流程

### 本地開發

```bash
# 設置開發環境
npm run env:dev

# 啟動開發服務器
npm run dev
```

#### Stripe Webhook 本地測試

在開發環境中，你需要同時運行 Stripe CLI 來接收 webhook 事件：

**1. 安裝 Stripe CLI**
```bash
# macOS (使用 Homebrew)
brew install stripe/stripe-cli/stripe

# Windows (使用 Scoop)
scoop bucket add stripe https://github.com/stripe/scoop-stripe-cli.git
scoop install stripe

# 或者從 https://github.com/stripe/stripe-cli/releases 下載
```

**2. 登入 Stripe CLI**
```bash
stripe login
```

**3. 啟動 webhook 監聽器**
```bash
# 在新的終端窗口中運行
npm run stripe:listen

# 或者直接使用 Stripe CLI
stripe listen --forward-to localhost:3000/api/webhooks/stripe
```

**4. 完整的開發流程**
```bash
# 終端 1: 啟動 Next.js 開發服務器
npm run dev

# 終端 2: 啟動 Stripe webhook 監聽器
npm run stripe:listen
```

**重要提醒：**
- Stripe CLI 會提供一個 webhook 簽名密鑰，需要將其添加到 `.env.local` 中的 `STRIPE_WEBHOOK_SECRET`
- 每次重新啟動 `stripe listen` 都會生成新的簽名密鑰
- 確保在測試支付流程時兩個服務都在運行

### 測試部署

```bash
# 部署到開發環境進行測試
npm run deploy:dev
```

### 生產部署

```bash
# 部署到生產環境
npm run deploy:prod
```

## 🔍 故障排除

### 常見問題

1. **環境變數未設置**
   - 檢查 `.env.development` 或 `.env.production` 文件
   - 確保所有必要變數都有實際值（不是 `your-xxx` 佔位符）

2. **Firebase 專案權限**
   - 確保你有對應 Firebase 專案的部署權限
   - 檢查 Firebase CLI 是否已正確登入

3. **Stripe 配置錯誤**
   - 確保使用正確的 Stripe 密鑰（開發環境用 test 密鑰，生產環境用 live 密鑰）
   - 檢查 Stripe Price IDs 是否正確

### 調試命令

```bash
# 檢查當前 Firebase 專案
firebase projects:list

# 檢查環境變數
cat .env.local

# 手動構建測試
npm run build

# 檢查 Firebase 配置
firebase use --add
```

## 📝 最佳實踐

1. **永遠不要提交 `.env.local` 到版本控制**
2. **定期更新環境變數模板文件**
3. **在部署前先在開發環境測試**
4. **保持開發和生產環境配置同步**
5. **使用不同的 Firebase 專案和 Stripe 帳戶**
