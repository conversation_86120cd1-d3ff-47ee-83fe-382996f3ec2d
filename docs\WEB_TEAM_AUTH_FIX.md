# 🔧 Web 團隊認證問題修復指南

**問題**: Web 團隊調用 Firebase Functions 時遇到 500 和 400 錯誤  
**根本原因**: 環境變數配置錯誤 + Firebase Auth Token 問題  

---

## 🚨 **立即修復**

### 1. **修正 BACKEND_API_URL**

**錯誤配置**:
```bash
BACKEND_API_URL="https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/backend-api"
```

**正確配置**:
```bash
BACKEND_API_URL="https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net"
```

**⚠️ 重要**: 移除 `/backend-api` 路徑！

### 2. **正確的 API 調用格式**

**錯誤調用**:
```javascript
const response = await fetch(`${BACKEND_API_URL}/get_user_dashboard`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(data)
});
```

**正確調用**:
```javascript
const response = await fetch(`${BACKEND_API_URL}/get_user_dashboard`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(data)
});
```

### 3. **Firebase Auth Token 獲取**

**確保正確獲取 Firebase Auth Token**:
```javascript
import { getAuth } from 'firebase/auth';

const getFirebaseToken = async () => {
  const auth = getAuth();
  const user = auth.currentUser;
  
  if (!user) {
    throw new Error('用戶未登入');
  }
  
  // 獲取 ID Token
  const token = await user.getIdToken();
  return token;
};

// 使用範例
const callAPI = async (endpoint, data) => {
  try {
    const token = await getFirebaseToken();
    
    const response = await fetch(`${BACKEND_API_URL}/${endpoint}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    if (!response.ok) {
      throw new Error(`API 調用失敗: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('API 調用錯誤:', error);
    throw error;
  }
};
```

---

## 🔍 **錯誤診斷**

### **500 錯誤 (get_user_dashboard)**

**可能原因**:
1. ❌ Firebase Auth Token 無效或過期
2. ❌ 用戶未在 Firebase Auth 中註冊
3. ❌ 請求體格式錯誤

**解決方案**:
```javascript
// 1. 檢查用戶登入狀態
const auth = getAuth();
const user = auth.currentUser;
console.log('當前用戶:', user);

// 2. 檢查 Token 有效性
const token = await user.getIdToken();
console.log('Token 長度:', token.length);

// 3. 檢查請求體 (get_user_dashboard 不需要請求體)
const response = await fetch(`${BACKEND_API_URL}/get_user_dashboard`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
  // 不需要 body
});
```

### **400 錯誤 (remove_device)**

**可能原因**:
1. ❌ 缺少必要參數 `device_id`
2. ❌ 請求體格式錯誤
3. ❌ device_id 格式無效

**解決方案**:
```javascript
// 正確的 remove_device 調用
const removeDevice = async (deviceId, reason = 'user_request') => {
  const token = await getFirebaseToken();
  
  const response = await fetch(`${BACKEND_API_URL}/remove_device`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      device_id: deviceId,  // 必要參數
      reason: reason        // 可選參數
    })
  });
  
  if (!response.ok) {
    const errorText = await response.text();
    console.error('remove_device 錯誤:', errorText);
    throw new Error(`移除設備失敗: ${response.status}`);
  }
  
  return await response.json();
};
```

---

## 🧪 **測試腳本**

### **完整的 API 測試**

```javascript
// test-api.js
import { initializeApp } from 'firebase/app';
import { getAuth, signInWithEmailAndPassword } from 'firebase/auth';

const firebaseConfig = {
  apiKey: "AIzaSyAHmnBBM3pVQTWWX5t5034_ffw0YxekNAo",
  authDomain: "speakoneai-dev-9f995.firebaseapp.com",
  projectId: "speakoneai-dev-9f995",
  storageBucket: "speakoneai-dev-9f995.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:4d2d174bdc3aceb8a11fbe"
};

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

const BACKEND_API_URL = "https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net";

const testAPI = async () => {
  try {
    // 1. 登入用戶
    console.log('🔐 登入用戶...');
    const userCredential = await signInWithEmailAndPassword(auth, '<EMAIL>', 'password');
    const user = userCredential.user;
    console.log('✅ 用戶登入成功:', user.uid);
    
    // 2. 獲取 Token
    console.log('🎫 獲取 Auth Token...');
    const token = await user.getIdToken();
    console.log('✅ Token 獲取成功, 長度:', token.length);
    
    // 3. 測試 get_user_dashboard
    console.log('📊 測試 get_user_dashboard...');
    const dashboardResponse = await fetch(`${BACKEND_API_URL}/get_user_dashboard`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (dashboardResponse.ok) {
      const dashboardData = await dashboardResponse.json();
      console.log('✅ get_user_dashboard 成功:', dashboardData);
    } else {
      const errorText = await dashboardResponse.text();
      console.error('❌ get_user_dashboard 失敗:', dashboardResponse.status, errorText);
    }
    
    // 4. 測試 remove_device (如果有設備)
    console.log('🗑️ 測試 remove_device...');
    const removeResponse = await fetch(`${BACKEND_API_URL}/remove_device`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        device_id: 'test-device-id',
        reason: 'test'
      })
    });
    
    if (removeResponse.ok) {
      const removeData = await removeResponse.json();
      console.log('✅ remove_device 成功:', removeData);
    } else {
      const errorText = await removeResponse.text();
      console.error('❌ remove_device 失敗:', removeResponse.status, errorText);
    }
    
  } catch (error) {
    console.error('🚨 測試失敗:', error);
  }
};

// 執行測試
testAPI();
```

---

## 📋 **檢查清單**

### **環境變數檢查**
- [ ] `BACKEND_API_URL` 移除 `/backend-api` 路徑
- [ ] Firebase 配置正確
- [ ] 所有必要的環境變數都已設置

### **代碼檢查**
- [ ] 使用正確的 Firebase Auth Token 獲取方式
- [ ] API 調用包含正確的 Authorization header
- [ ] 請求體格式正確
- [ ] 錯誤處理完整

### **測試檢查**
- [ ] 用戶可以成功登入 Firebase Auth
- [ ] Token 可以正確獲取
- [ ] get_user_dashboard 調用成功
- [ ] remove_device 調用成功

---

## 🎯 **立即行動項目**

1. **修改 .env 文件**:
   ```bash
   # 修改這一行
   BACKEND_API_URL="https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net"
   ```

2. **檢查 Firebase Auth 初始化**:
   ```javascript
   // 確保 Firebase 正確初始化
   const app = initializeApp(firebaseConfig);
   const auth = getAuth(app);
   ```

3. **測試 API 調用**:
   ```javascript
   // 使用上面的測試腳本
   ```

4. **檢查瀏覽器控制台**:
   - 查看網路請求
   - 檢查錯誤訊息
   - 驗證 Token 格式

**修復這些問題後，500 和 400 錯誤應該會解決！** 🚀
