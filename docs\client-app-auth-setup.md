# 客戶端應用程式 Firebase Authentication 設置

## 🖥️ Windows/macOS 客戶端應用程式

### Firebase 配置

在你的客戶端應用程式中使用以下 Firebase 配置：

```javascript
// firebase-config.js
const firebaseConfig = {
  apiKey: "AIzaSyClbeEq7o4kXcDLcO40r7KhBAmIfIMVARA",
  authDomain: "speechpilot-f1495.firebaseapp.com",
  projectId: "speechpilot-f1495",
  storageBucket: "speechpilot-f1495.firebasestorage.app",
  messagingSenderId: "1005638354561",
  appId: "1:1005638354561:web:04959935ed09084298af80"
};

// 初始化 Firebase
import { initializeApp } from 'firebase/app';
import { getAuth, GoogleAuthProvider, signInWithPopup } from 'firebase/auth';

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const googleProvider = new GoogleAuthProvider();
```

### 登入實現

```javascript
// auth.js
import { auth, googleProvider } from './firebase-config.js';
import { signInWithPopup, signOut } from 'firebase/auth';

// Google 登入
export async function signInWithGoogle() {
  try {
    const result = await signInWithPopup(auth, googleProvider);
    const user = result.user;
    
    // 獲取 ID Token 用於 API 調用
    const idToken = await user.getIdToken();
    
    // 儲存用戶資訊和 token
    localStorage.setItem('userToken', idToken);
    localStorage.setItem('userInfo', JSON.stringify({
      uid: user.uid,
      email: user.email,
      name: user.displayName,
      photo: user.photoURL
    }));
    
    return { success: true, user };
  } catch (error) {
    console.error('登入失敗:', error);
    return { success: false, error: error.message };
  }
}

// 登出
export async function signOutUser() {
  try {
    await signOut(auth);
    localStorage.removeItem('userToken');
    localStorage.removeItem('userInfo');
    return { success: true };
  } catch (error) {
    console.error('登出失敗:', error);
    return { success: false, error: error.message };
  }
}

// 檢查登入狀態
export function getCurrentUser() {
  return new Promise((resolve) => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      unsubscribe();
      resolve(user);
    });
  });
}
```

### API 調用驗證

```javascript
// api.js
// 調用網站 API 時使用 Firebase ID Token
export async function callWebsiteAPI(endpoint, data = {}) {
  const token = localStorage.getItem('userToken');
  
  if (!token) {
    throw new Error('用戶未登入');
  }
  
  const response = await fetch(`https://your-website.com/api/${endpoint}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(data)
  });
  
  if (!response.ok) {
    throw new Error('API 調用失敗');
  }
  
  return response.json();
}

// 使用範例
export async function trackUsage(sessionData) {
  return callWebsiteAPI('usage/track', sessionData);
}

export async function checkSubscription() {
  return callWebsiteAPI('subscription/status');
}
```

## 📱 手機應用程式（iOS/Android）

### iOS (Swift)

```swift
// FirebaseConfig.swift
import Firebase
import FirebaseAuth

class AuthManager: ObservableObject {
    @Published var user: User?
    @Published var isSignedIn = false
    
    init() {
        FirebaseApp.configure()
        Auth.auth().addStateDidChangeListener { auth, user in
            self.user = user
            self.isSignedIn = user != nil
        }
    }
    
    func signInWithGoogle() {
        // 實現 Google 登入
        // 使用 GoogleSignIn SDK
    }
    
    func signOut() {
        try? Auth.auth().signOut()
    }
}
```

### Android (Kotlin)

```kotlin
// AuthManager.kt
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.GoogleAuthProvider

class AuthManager {
    private val auth = FirebaseAuth.getInstance()
    
    fun signInWithGoogle(idToken: String, callback: (Boolean) -> Unit) {
        val credential = GoogleAuthProvider.getCredential(idToken, null)
        auth.signInWithCredential(credential)
            .addOnCompleteListener { task ->
                callback(task.isSuccessful)
            }
    }
    
    fun signOut() {
        auth.signOut()
    }
    
    fun getCurrentUser() = auth.currentUser
}
```

## 🔗 統一用戶體驗

### 1. 共享用戶資料
- 所有平台使用相同的 Firebase 專案
- 用戶在任何平台登入後，資料同步到 Firestore
- 訂閱狀態、使用記錄等資料統一管理

### 2. 跨平台同步
- 用戶在網站購買訂閱 → 客戶端應用程式立即可用
- 客戶端應用程式使用記錄 → 網站即時更新
- 設備管理統一控制

### 3. 安全性
- 所有 API 調用使用 Firebase ID Token 驗證
- Token 自動刷新，無需手動處理
- 統一的權限控制和安全規則

## 🛠️ 實施步驟

1. **網站端**：已經設置完成
2. **客戶端應用程式**：整合上述 Firebase Auth 代碼
3. **手機應用程式**：使用 Firebase SDK 實現登入
4. **API 驗證**：在網站 API 中驗證 Firebase ID Token

這樣所有平台都使用相同的 Firebase Authentication 系統，提供統一的用戶體驗！
