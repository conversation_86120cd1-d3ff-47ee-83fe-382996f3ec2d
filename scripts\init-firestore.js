#!/usr/bin/env node

/**
 * Firestore Schema Initialization Script
 * 
 * This script initializes the Firestore database with the required collections
 * and sample data structure for the SpeechPilot application.
 */

const admin = require('firebase-admin');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

// Initialize Firebase Admin SDK
const serviceAccount = {
  type: "service_account",
  project_id: process.env.FIREBASE_PROJECT_ID,
  private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
  private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  client_email: process.env.FIREBASE_CLIENT_EMAIL,
  client_id: process.env.FIREBASE_CLIENT_ID,
  auth_uri: "https://accounts.google.com/o/oauth2/auth",
  token_uri: "https://oauth2.googleapis.com/token",
  auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
  client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL}`
};

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: process.env.FIREBASE_PROJECT_ID
  });
}

const db = admin.firestore();

async function initializeFirestore() {
  console.log('🚀 Initializing Firestore schema...');
  
  try {
    // 1. Create a sample user document
    console.log('📝 Creating sample user document...');
    const sampleUserId = 'sample-user-123';
    const sampleUserRef = db.collection('users').doc(sampleUserId);
    
    await sampleUserRef.set({
      id: sampleUserId,
      email: '<EMAIL>',
      name: 'Sample User',
      image: '',
      createdAt: admin.firestore.Timestamp.now(),
      usageSeconds: 0,
      subscriptionPlan: 'FREE',
      subscriptionStatus: null,
      dailyUsageSeconds: 0,
      lastUsageReset: admin.firestore.Timestamp.now(),
      stripeCustomerId: null
    });
    
    console.log('✅ Sample user created');

    // 2. Create a sample subscription document
    console.log('📝 Creating sample subscription document...');
    const sampleSubscriptionRef = db.collection('subscriptions').doc();
    
    await sampleSubscriptionRef.set({
      id: sampleSubscriptionRef.id,
      userId: sampleUserId,
      stripeSubscriptionId: 'sub_sample123',
      stripePriceId: 'price_sample123',
      plan: 'FREE',
      status: 'active',
      currentPeriodStart: admin.firestore.Timestamp.now(),
      currentPeriodEnd: admin.firestore.Timestamp.fromDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)), // 30 days from now
      cancelAtPeriodEnd: false,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    });
    
    console.log('✅ Sample subscription created');

    // 3. Create a sample purchase document
    console.log('📝 Creating sample purchase document...');
    const samplePurchaseRef = db.collection('purchases').doc();
    
    await samplePurchaseRef.set({
      id: samplePurchaseRef.id,
      userId: sampleUserId,
      productType: 'SUBSCRIPTION',
      stripePaymentIntentId: 'pi_sample123',
      stripeSubscriptionId: 'sub_sample123',
      amount: 999, // $9.99 in cents
      currency: 'usd',
      status: 'completed',
      createdAt: admin.firestore.Timestamp.now()
    });
    
    console.log('✅ Sample purchase created');

    // 4. Create a sample usage log document
    console.log('📝 Creating sample usage log document...');
    const sampleUsageRef = db.collection('usage_logs').doc();
    
    await sampleUsageRef.set({
      id: sampleUsageRef.id,
      userId: sampleUserId,
      deviceId: 'device-sample-123',
      sessionStart: admin.firestore.Timestamp.now(),
      sessionEnd: admin.firestore.Timestamp.fromDate(new Date(Date.now() + 60 * 60 * 1000)), // 1 hour later
      secondsUsed: 3600, // 1 hour
      platform: 'windows',
      appVersion: '1.0.0',
      features: ['speech-to-text', 'text-to-speech'],
      createdAt: admin.firestore.Timestamp.now()
    });
    
    console.log('✅ Sample usage log created');

    // 5. Create a sample device document
    console.log('📝 Creating sample device document...');
    const sampleDeviceRef = db.collection('devices').doc();
    
    await sampleDeviceRef.set({
      id: sampleDeviceRef.id,
      userId: sampleUserId,
      deviceId: 'device-sample-123',
      deviceName: 'Sample Windows PC',
      platform: 'windows',
      lastActiveAt: admin.firestore.Timestamp.now(),
      isActive: true,
      createdAt: admin.firestore.Timestamp.now()
    });
    
    console.log('✅ Sample device created');

    // 6. Create indexes info document (for reference)
    console.log('📝 Creating indexes info document...');
    const indexesRef = db.collection('_metadata').doc('indexes');
    
    await indexesRef.set({
      description: 'Required Firestore indexes for SpeechPilot',
      indexes: [
        {
          collection: 'users',
          fields: ['email'],
          note: 'For getUserByEmail queries'
        },
        {
          collection: 'subscriptions',
          fields: ['userId', 'status'],
          note: 'For getUserActiveSubscription queries'
        },
        {
          collection: 'purchases',
          fields: ['userId', 'createdAt'],
          note: 'For getPurchasesByUserId queries'
        },
        {
          collection: 'usage_logs',
          fields: ['userId', 'createdAt'],
          note: 'For getRecentUsageLogs queries'
        },
        {
          collection: 'devices',
          fields: ['userId', 'isActive', 'lastActiveAt'],
          note: 'For getUserDevices queries'
        }
      ],
      createdAt: admin.firestore.Timestamp.now()
    });
    
    console.log('✅ Indexes info created');

    console.log('\n🎉 Firestore schema initialization completed successfully!');
    console.log('\n📊 Collections created:');
    console.log('  - users (with sample data)');
    console.log('  - subscriptions (with sample data)');
    console.log('  - purchases (with sample data)');
    console.log('  - usage_logs (with sample data)');
    console.log('  - devices (with sample data)');
    console.log('  - _metadata (with indexes info)');
    
    console.log('\n🔧 Next steps:');
    console.log('  1. Check Firebase Console to verify collections');
    console.log('  2. Set up required Firestore indexes (see _metadata/indexes document)');
    console.log('  3. Re-enable database operations in your auth.ts file');
    console.log('  4. Test the complete authentication flow');
    
  } catch (error) {
    console.error('❌ Error initializing Firestore:', error);
    process.exit(1);
  }
}

// Run the initialization
initializeFirestore()
  .then(() => {
    console.log('\n✨ Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
