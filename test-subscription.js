// Test script to simulate Stripe subscription checkout
// This script will trigger a test webhook event

// Load environment variables first
require('dotenv').config({ path: '.env.local' });

const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

async function testSubscriptionFlow() {
  try {
    console.log('🧪 Testing Stripe subscription flow...');

    // Create a test customer
    const customer = await stripe.customers.create({
      email: '<EMAIL>',
      name: 'Test User',
    });
    console.log('✅ Created test customer:', customer.id);

    // Create a checkout session for PRO monthly plan
    const session = await stripe.checkout.sessions.create({
      customer: customer.id,
      payment_method_types: ['card'],
      mode: 'subscription',
      line_items: [
        {
          price: process.env.STRIPE_PRICE_ID_PRO_MONTHLY,
          quantity: 1,
        },
      ],
      success_url: 'http://localhost:3000/dashboard?session_id={CHECKOUT_SESSION_ID}',
      cancel_url: 'http://localhost:3000/pricing?cancelled=true',
    });

    console.log('✅ Created checkout session:', session.id);
    console.log('🔗 Checkout URL:', session.url);

    // Simulate successful payment by creating a subscription directly
    const subscription = await stripe.subscriptions.create({
      customer: customer.id,
      items: [
        {
          price: process.env.STRIPE_PRICE_ID_PRO_MONTHLY,
        },
      ],
      payment_behavior: 'default_incomplete',
      payment_settings: { save_default_payment_method: 'on_subscription' },
      expand: ['latest_invoice.payment_intent'],
    });

    console.log('✅ Created subscription:', subscription.id);

    // Trigger webhook event manually
    console.log('🔔 Triggering webhook events...');
    
    // You can use Stripe CLI to trigger events:
    console.log('Run this command to trigger checkout.session.completed:');
    console.log(`stripe trigger checkout.session.completed --override checkout_session:customer_email=<EMAIL> --override checkout_session:subscription=${subscription.id}`);

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

if (require.main === module) {
  testSubscriptionFlow();
}

module.exports = { testSubscriptionFlow };
