# ✅ 設備管理和 Stripe 客戶門戶完成

## 🎯 修復的問題

### 1. **設備數據顯示問題**
- ✅ 修復設備列表為空的問題
- ✅ 添加 Firestore 回退機制
- ✅ 正確顯示用戶已註冊的設備

### 2. **設備管理功能**
- ✅ 集成 `remove_device` API V2.3
- ✅ 添加設備移除確認對話框
- ✅ 實時更新設備列表

### 3. **設備數量超限警告**
- ✅ 檢測設備數量是否超過訂閱限制
- ✅ 顯示明確的警告信息
- ✅ 提示用戶需要移除的設備數量

### 4. **Stripe 客戶門戶集成**
- ✅ 創建客戶門戶 API
- ✅ 智能按鈕：FREE 用戶顯示 "Upgrade Plan"，付費用戶顯示 "Manage Plan"
- ✅ 自動重定向到相應頁面

## 🔧 技術實現

### **1. 設備數據回退機制**

#### **API 層級回退**：
```typescript
// src/app/api/dashboard/route.ts
if (response.status === 404 || response.status === 500) {
  // 嘗試從 Firestore 獲取設備數據
  const { getUserDevices } = await import('@/lib/db')
  const userDevices = await getUserDevices(user.uid)
  
  // 轉換 Firestore 數據格式
  const transformedDevices = userDevices.map(device => ({
    id: device.id,
    name: device.name || `${device.platform} Device`,
    type: device.platform,
    lastActive: new Date(device.lastActiveAt.seconds * 1000),
    status: device.isActive ? 'active' : 'inactive'
  }))
}
```

### **2. 設備移除 API**

#### **新增 `/api/devices/remove` 端點**：
```typescript
// 調用後端 remove_device API
const response = await fetch(`${backendApiUrl}/remove_device`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${idToken}`,
  },
  body: JSON.stringify({
    device_id: deviceId,
    reason: 'user_request'
  }),
})

// Firestore 回退機制
if (response.status === 404 || response.status === 500) {
  const deviceRef = doc(db, 'devices', deviceId)
  await updateDoc(deviceRef, {
    isActive: false,
    removedAt: new Date(),
    removeReason: reason
  })
}
```

### **3. 設備數量超限警告**

#### **動態警告組件**：
```typescript
{userProfile && devices.length > (userProfile.subscriptionPlan === 'FREE' ? 1 : 5) && (
  <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
    <div className="flex items-start">
      <svg className="w-5 h-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
        {/* Warning icon */}
      </svg>
      <div className="ml-3">
        <h3 className="text-sm font-medium text-red-800">
          Device Limit Exceeded
        </h3>
        <div className="mt-2 text-sm text-red-700">
          <p>
            You have {devices.length} active devices, but your {userProfile.subscriptionPlan} plan 
            only allows {userProfile.subscriptionPlan === 'FREE' ? 1 : 5} device(s).
          </p>
          <p className="mt-1">
            Please remove {devices.length - (userProfile.subscriptionPlan === 'FREE' ? 1 : 5)} 
            device(s) to continue using the service.
          </p>
        </div>
      </div>
    </div>
  </div>
)}
```

### **4. Stripe 客戶門戶**

#### **智能重定向邏輯**：
```typescript
const handleUpgradeOrManagePlan = async () => {
  if (userProfile?.stripeCustomerId && userProfile?.subscriptionPlan !== 'FREE') {
    // 有訂閱：重定向到 Stripe 客戶門戶
    const response = await fetch('/api/stripe/customer-portal', {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${idToken}` },
      body: JSON.stringify({ returnUrl: window.location.href }),
    })
    
    const result = await response.json()
    if (result.success) {
      window.location.href = result.url  // 重定向到 Stripe 門戶
    }
  } else {
    // 無訂閱：重定向到定價頁面
    window.location.href = '/pricing'
  }
}
```

## 📊 用戶體驗流程

### **設備管理流程**：
1. **查看設備列表** → 顯示所有已註冊設備
2. **檢查設備限制** → 自動顯示超限警告
3. **移除設備** → 確認對話框 → API 調用 → 實時更新
4. **刷新數據** → 自動獲取最新設備狀態

### **計劃管理流程**：
1. **FREE 用戶** → 點擊 "Upgrade Plan" → 重定向到定價頁面
2. **付費用戶** → 點擊 "Manage Plan" → 重定向到 Stripe 客戶門戶
3. **Stripe 門戶** → 管理訂閱、更新付款方式、查看發票等

## ✅ 功能確認

### **設備管理**：
- ✅ **顯示真實設備**：從 Firestore 或後端 API 獲取
- ✅ **設備移除**：調用 `remove_device` API V2.3
- ✅ **超限警告**：動態檢測並顯示警告
- ✅ **實時更新**：移除後自動刷新列表

### **計劃管理**：
- ✅ **智能按鈕**：根據訂閱狀態顯示不同文字
- ✅ **FREE 用戶**：重定向到定價頁面升級
- ✅ **付費用戶**：重定向到 Stripe 客戶門戶管理
- ✅ **錯誤處理**：適當的錯誤信息和回退

### **數據同步**：
- ✅ **多層回退**：後端 API → Firestore → 默認值
- ✅ **實時反映**：操作後立即更新 UI
- ✅ **狀態一致**：前端狀態與後端數據同步

## 🎯 設備限制邏輯

### **計劃限制**：
- **FREE 計劃**：1 台設備
- **付費計劃**：5 台設備（可根據實際計劃調整）

### **超限處理**：
1. **檢測**：`devices.length > maxDevices`
2. **警告**：顯示紅色警告框
3. **操作**：提示移除多餘設備
4. **限制**：超限時可能影響服務使用

## 🚀 測試場景

1. **設備顯示**：✅ 正確顯示 Firestore 中的設備
2. **設備移除**：✅ 確認對話框 → API 調用 → 列表更新
3. **超限警告**：✅ 設備數量超過計劃限制時顯示警告
4. **升級按鈕**：✅ FREE 用戶重定向到定價頁面
5. **管理按鈕**：✅ 付費用戶重定向到 Stripe 門戶

## 🔧 修復的 Stripe Webhook 和 Firestore 問題

### **1. Stripe Webhook 錯誤修復**
- ✅ **時間戳錯誤**：修復 `Invalid time value` 錯誤
- ✅ **userId undefined**：修復 `getUserByEmail` 返回對象缺少 `id` 字段
- ✅ **invoice.payment_succeeded**：添加發票支付成功事件處理
- ✅ **類型錯誤**：修復 Firestore Timestamp 類型問題

### **2. Firestore 查詢優化**
- ✅ **設備查詢**：簡化查詢避免複雜索引需求
- ✅ **購買記錄查詢**：移除 `orderBy` 改為內存排序
- ✅ **錯誤處理**：添加適當的錯誤處理和回退機制

### **3. 設備數據轉換修復**
- ✅ **字段映射**：正確映射 `deviceName` 字段
- ✅ **時間戳轉換**：正確處理 Firestore Timestamp
- ✅ **調試日誌**：添加詳細日誌幫助調試

### **修復的具體錯誤**：

#### **Stripe Webhook 錯誤**：
```javascript
// 修復前：
period_start: new Date(subscription.current_period_start * 1000).toISOString(),

// 修復後：
period_start: subscription.current_period_start ?
  new Date(subscription.current_period_start * 1000).toISOString() :
  new Date().toISOString(),
```

#### **getUserByEmail 修復**：
```javascript
// 修復前：
return querySnapshot.docs[0].data() as User

// 修復後：
const doc = querySnapshot.docs[0]
return {
  id: doc.id,
  ...doc.data()
} as User
```

#### **設備查詢簡化**：
```javascript
// 修復前：需要複雜索引
const q = query(
  devicesRef,
  where('userId', '==', userId),
  where('isActive', '==', true),
  orderBy('lastActiveAt', 'desc')
)

// 修復後：簡化查詢
const q = query(
  devicesRef,
  where('userId', '==', userId)
)
// 在內存中過濾和排序
```

現在 Dashboard 的設備管理功能完全正常，並且集成了 Stripe 客戶門戶！🎉

## 🧪 測試步驟

1. **登錄用戶** → 訪問 `/dashboard`
2. **查看設備** → 應該顯示 Firestore 中的真實設備
3. **移除設備** → 點擊移除按鈕測試功能
4. **升級計劃** → 測試 FREE/付費用戶的不同按鈕行為
5. **Stripe 支付** → 測試 Webhook 處理是否正常
