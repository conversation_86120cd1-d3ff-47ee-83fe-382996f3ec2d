NEXT_PUBLIC_FIREBASE_API_KEY="AIzaSyAHmnBBM3pVQTWWX5t5034_ffw0YxekNAo"
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN="speakoneai-dev-9f995.firebaseapp.com"
NEXT_PUBLIC_FIREBASE_PROJECT_ID="speakoneai-dev-9f995"
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET="speakoneai-dev-9f995.firebasestorage.app"
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID="368906331419"
NEXT_PUBLIC_FIREBASE_APP_ID="1:368906331419:web:4d2d174bdc3aceb8a11fbe"
GOOGLE_ANALYTICS_MEASUREMENT_ID=""

# Google OAuth Configuration (Get from Google Cloud Console)
# Replace with your actual Google OAuth credentials from the existing OAuth client
GOOGLE_CLIENT_ID="368906331419-1f3st33fenburv75is3c4m6mjtk8kkkn.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"\

STRIPE_PUBLISHABLE_KEY="pk_test_51RoRGqRZ0KQQwnzrqYucbabuHwNHzRs3EEptBjv1ww66Q3v7YF375U24b64G8MPnYIKyYF5HQiwProT2qnr5EvOe00BSVIqY5z"
STRIPE_SECRET_KEY="sk_test_51RoRGqRZ0KQQwnzrLo14jtgBocHBmaEcNjPsYbjtWqPUfFTFtfHcI9BfkyDXNsC9bnOjSwNAEeI89jvdTg3Pe7Bi00N2Rmk5e9"
STRIPE_WEBHOOK_SECRET="whsec_ea54957e2b0c22221b25d82a5e050c2d202682f3aed358e5bd85036aa92d8c6e"

NEXT_PUBLIC_SITE_URL="http://localhost:3000"
NEXT_PUBLIC_APP_NAME="SpeakOneAI"

NODE_ENV="development"

STRIPE_PRICE_ID_STARTER_MONTHLY="price_1RoZKMRZ0KQQwnzrK6j68YRe"
STRIPE_PRICE_ID_STARTER_YEARLY="price_1RoZKNRZ0KQQwnzr4koknbV6"
STRIPE_PRICE_ID_PRO_MONTHLY="price_1RoZKNRZ0KQQwnzrsFzMldYu"
STRIPE_PRICE_ID_PRO_YEARLY="price_1RoZKORZ0KQQwnzr8zFJqEvt"
STRIPE_PRICE_ID_PREMIUM_MONTHLY="price_1RoZKORZ0KQQwnzrl2Eew2VK"
STRIPE_PRICE_ID_PREMIUM_YEARLY="price_1RoZKPRZ0KQQwnzrFYwoG8sd"
STRIPE_PRICE_ID_MAX_MONTHLY="price_1RoZKPRZ0KQQwnzrzw8RYyF0"
STRIPE_PRICE_ID_MAX_YEARLY="price_1RoZKQRZ0KQQwnzrxAgGnQ2Z"

# Firebase Admin (Server-side) - Development
# Note: These are placeholder values. Replace with actual Firebase Admin SDK credentials
# FIREBASE_PROJECT_ID="speakoneai-dev-9f995"
# FIREBASE_CLIENT_EMAIL="<EMAIL>"
# FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-actual-private-key\n-----END PRIVATE KEY-----\n"

# Backend API URL (Development)
BACKEND_API_URL="https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/backend-api"