import { NextRequest, NextResponse } from 'next/server'
import { stripe, getPriceId } from '@/lib/stripe'
import { verifyFirebaseToken } from '@/lib/firebase-auth-middleware'

export async function POST(request: NextRequest) {
  try {
    const { planId, billingPeriod } = await request.json()

    // Validate required fields
    if (!planId || !billingPeriod) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Verify Firebase authentication
    const user = await verifyFirebaseToken(request)
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get user ID for checkout session
    const userId = user.uid
    console.log('✅ User authenticated for checkout:', userId)

    // Get the price ID for the selected plan and billing period
    const priceId = getPriceId(planId, billingPeriod)
    if (!priceId) {
      return NextResponse.json(
        { error: 'Invalid plan or billing period' },
        { status: 400 }
      )
    }

    // Create Stripe checkout session
    if (!stripe) {
      return NextResponse.json(
        { error: 'Stripe not initialized' },
        { status: 500 }
      )
    }

    const session = await stripe.checkout.sessions.create({
      mode: 'subscription',
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      success_url: `${process.env.NEXT_PUBLIC_SITE_URL}/dashboard?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL}/pricing?cancelled=true`,
      customer_email: user.email,
      metadata: {
        userId: userId,
        planId: planId,
        billingPeriod: billingPeriod,
      },
      subscription_data: {
        metadata: {
          userId: userId,
          planId: planId,
        },
      },
    })

    return NextResponse.json({
      sessionId: session.id,
      url: session.url
    })

  } catch (error: any) {
    console.error('Checkout error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
