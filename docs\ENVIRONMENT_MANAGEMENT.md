# 🔧 Environment Management Guide

This guide explains how to manage multiple Firebase environments (development and production) in the SpeakOneAI project using the new build-time environment switching system.

## 📋 Overview

The project supports two environments:
- **Development** (`speakoneai-dev-9f995`): For testing and development
- **Production** (`speakoneai-prod`): For live production use

## 🚀 Quick Start

### Development Workflow
```bash
# Set development environment and start dev server
npm run setenv:dev
npm run dev
```

### Production Deployment
```bash
# Deploy to production (automatically sets env, builds, and deploys)
npm run deploy:prod
```

### Development Deployment
```bash
# Deploy to development (automatically sets env, builds, and deploys)
npm run deploy:dev
```

## 📦 New NPM Scripts

### Environment Setting
```bash
npm run setenv:dev    # Copy .env.dev to .env.local
npm run setenv:prod   # Copy .env.prod to .env.local
```

### Building with Environment
```bash
npm run build:dev     # Set dev env + build
npm run build:prod    # Set prod env + build
```

### Complete Deployment
```bash
npm run deploy:dev    # Switch Firebase project + build:dev + deploy
npm run deploy:prod   # Switch Firebase project + build:prod + deploy
```

## 📁 Environment Files

### `.env.dev`
Contains development-specific configuration:
- Firebase project: `speakoneai-dev-9f995`
- Stripe test keys
- Development URLs
- Debug settings enabled

### `.env.prod`
Contains production-specific configuration:
- Firebase project: `speakoneai-prod`
- Stripe live keys
- Production URLs
- Debug settings disabled

### `.env.local`
This is the active environment file that gets automatically generated when you run `setenv:dev` or `setenv:prod`. **Do not edit this file manually.**

## 🔄 How It Works

1. **Pre-Build Environment Copy**: Before building, the correct environment file (`.env.dev` or `.env.prod`) is copied to `.env.local`
2. **Next.js Build**: Next.js reads `.env.local` during build time and embeds `NEXT_PUBLIC_*` variables
3. **Firebase Project Switch**: The deployment script switches to the correct Firebase project
4. **Deploy**: The built application is deployed to the correct Firebase project
5. **Cleanup**: `.env.local` is optionally removed after deployment

## 🔥 Firebase Project Setup

### 1. Create Firebase Projects

Create two separate Firebase projects:

1. **Development Project**: `speakoneai-dev`
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Create new project with ID: `speakoneai-dev`
   - Enable Authentication, Firestore, and Hosting

2. **Production Project**: `speakoneai-prod`
   - Create new project with ID: `speakoneai-prod`
   - Enable Authentication, Firestore, and Hosting

### 2. Configure Firebase CLI

```bash
# Install Firebase CLI (if not already installed)
npm install -g firebase-tools

# Login to Firebase
firebase login

# The project aliases are already configured in .firebaserc
```

### 3. Get Firebase Configuration

For each project, get the web app configuration:

1. Go to Project Settings > General
2. Scroll down to "Your apps"
3. Click on the web app or create one
4. Copy the configuration values

Update the respective environment files with the correct values.

## 🔄 Deployment Workflow

### Development Deployment
```bash
# Complete development deployment
npm run deploy:dev
# This does: firebase use dev → copy .env.dev → build → deploy → cleanup
```

### Production Deployment
```bash
# Complete production deployment
npm run deploy:prod
# This does: firebase use prod → copy .env.prod → build → deploy → cleanup
```

### Manual Environment Setting (for local development)
```bash
# Set development environment for local development
npm run setenv:dev
npm run dev

# Set production environment for local testing
npm run setenv:prod
npm run build
npm run start
```

## 📊 Environment Status

Check which environment is currently active:

```bash
npm run env:status
```

This will show:
- Current active environment
- Firebase project being used
- Source configuration file
- Environment description

## 🛠️ Development Workflow

### Starting Development
```bash
# 1. Switch to development environment
npm run env:dev

# 2. Start the development server
npm run dev
```

### Deploying to Production
```bash
# 1. Switch to production environment
npm run env:prod

# 2. Build the application
npm run build

# 3. Deploy to Firebase
npm run deploy:prod
```

## 🔐 Security Best Practices

1. **Never commit `.env.local`** - It's already in `.gitignore`
2. **Keep environment files secure** - They contain sensitive configuration
3. **Use different Stripe keys** for each environment
4. **Regularly rotate API keys** and service account credentials
5. **Monitor Firebase usage** in both projects

## 🐛 Troubleshooting

### Environment Not Switching
```bash
# Check if environment files exist
npm run env:list

# Manually check current environment
npm run env:status

# Force switch
node scripts/env-manager.js switch dev
```

### Firebase Project Issues
```bash
# Check current Firebase project
firebase projects:list

# Manually switch Firebase project
firebase use speakoneai-dev
# or
firebase use speakoneai-prod
```

### Missing Configuration
If you see warnings about missing Firebase configuration:

1. Check that the environment file exists
2. Verify all required fields are filled
3. Ensure you've switched to the correct environment
4. Check Firebase project settings

## 📝 Environment Variables Reference

### Required Firebase Variables
- `NEXT_PUBLIC_FIREBASE_API_KEY`
- `NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN`
- `NEXT_PUBLIC_FIREBASE_PROJECT_ID`
- `NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET`
- `NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID`
- `NEXT_PUBLIC_FIREBASE_APP_ID`

### Required Firebase Admin Variables
- `FIREBASE_PROJECT_ID`
- `FIREBASE_CLIENT_EMAIL`
- `FIREBASE_PRIVATE_KEY`

### Optional Variables
- `GOOGLE_ANALYTICS_MEASUREMENT_ID`
- `NEXT_PUBLIC_APP_ENV`
- `DEBUG`

## 🚀 Advanced Usage

### Custom Environment Script
You can also use the environment manager script directly:

```bash
# Show help
node scripts/env-manager.js help

# Switch environment
node scripts/env-manager.js switch dev

# Check status
node scripts/env-manager.js status

# List environments
node scripts/env-manager.js list
```

### Adding New Environments
To add a new environment (e.g., staging):

1. Create `.env.staging` file
2. Update `scripts/env-manager.js` to include the new environment
3. Add Firebase project alias in `.firebaserc`
4. Add npm script in `package.json`

## 📋 Summary Table

| Target | Env File Used | Command | Firebase Project | Description |
|--------|---------------|---------|------------------|-------------|
| Dev | `.env.dev` | `npm run deploy:dev` | `speakoneai-dev-9f995` | Deploy to development |
| Prod | `.env.prod` | `npm run deploy:prod` | `speakoneai-prod` | Deploy to production |
| Local Dev | `.env.dev` | `npm run setenv:dev && npm run dev` | N/A | Local development |
| Local Prod Test | `.env.prod` | `npm run setenv:prod && npm run build` | N/A | Local production testing |

## 🔧 Key Benefits

1. **Build-time Environment Switching**: Environment variables are embedded at build time, ensuring consistency
2. **Automated Deployment**: Single command deploys to the correct environment
3. **Cross-platform Compatibility**: Uses `shx` for Windows/Mac/Linux compatibility
4. **Clean Separation**: Clear separation between dev and prod configurations
5. **Firebase Project Management**: Automatic Firebase project switching
6. **Security**: `.env.local` is automatically cleaned up after deployment

## 📞 Support

If you encounter issues with environment management:

1. Check this documentation
2. Verify your Firebase project setup
3. Ensure all required environment variables are set
4. Check Firebase CLI authentication (`firebase login`)
5. Verify `shx` is installed (`npm install --save-dev shx`)
