import { NextRequest } from 'next/server'

export interface AuthenticatedUser {
  uid: string
  email: string
  name?: string
  picture?: string
}

/**
 * Verify Firebase ID Token using Google's public keys
 * This method doesn't require Firebase Admin SDK
 */
export async function verifyFirebaseToken(request: NextRequest): Promise<AuthenticatedUser | null> {
  try {
    const authHeader = request.headers.get('authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null
    }

    const idToken = authHeader.split('Bearer ')[1]

    if (!idToken) {
      return null
    }

    // Verify token using Google's public API
    const projectId = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID
    if (!projectId) {
      console.error('Firebase project ID not configured')
      return null
    }

    // Use Google's token verification endpoint
    const response = await fetch(
      `https://www.googleapis.com/identitytoolkit/v3/relyingparty/getAccountInfo?key=${process.env.NEXT_PUBLIC_FIREBASE_API_KEY}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          idToken: idToken,
        }),
      }
    )

    if (!response.ok) {
      console.error('Token verification failed:', response.status)
      return null
    }

    const data = await response.json()

    if (!data.users || data.users.length === 0) {
      return null
    }

    const user = data.users[0]

    return {
      uid: user.localId,
      email: user.email || '',
      name: user.displayName || user.email?.split('@')[0] || '',
      picture: user.photoUrl || '',
    }
  } catch (error) {
    console.error('Error verifying Firebase token:', error)
    return null
  }
}

/**
 * Middleware wrapper for API routes that require authentication
 */
export function withFirebaseAuth(handler: (request: NextRequest, user: AuthenticatedUser) => Promise<Response>) {
  return async (request: NextRequest) => {
    const user = await verifyFirebaseToken(request)
    
    if (!user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }
    
    return handler(request, user)
  }
}

/**
 * Get user from either NextAuth session (web) or Firebase token (client apps)
 */
export async function getAuthenticatedUser(request: NextRequest): Promise<AuthenticatedUser | null> {
  // First try Firebase token (for client apps)
  const firebaseUser = await verifyFirebaseToken(request)
  if (firebaseUser) {
    return firebaseUser
  }
  
  // If no Firebase token, this might be a web request with NextAuth session
  // In this case, the calling API route should handle NextAuth session separately
  return null
}
