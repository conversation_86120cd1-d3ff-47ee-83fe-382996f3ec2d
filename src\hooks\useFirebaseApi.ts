// Firebase API Hook - 統一的 API 調用接口
import { useState, useCallback } from 'react'
import { firebaseApi, handleFirebaseError } from '@/lib/firebase-api'

interface ApiState<T> {
  data: T | null
  loading: boolean
  error: string | null
}

export const useFirebaseApi = () => {
  // Dashboard API
  const [dashboardState, setDashboardState] = useState<ApiState<any>>({
    data: null,
    loading: false,
    error: null
  })

  // 獲取 Dashboard 數據
  const loadDashboard = useCallback(async () => {
    setDashboardState(prev => ({ ...prev, loading: true, error: null }))
    
    try {
      const result = await firebaseApi.getUserDashboard()
      
      if (result.success) {
        setDashboardState({
          data: result.data,
          loading: false,
          error: null
        })
        return result.data
      } else {
        throw new Error('API returned success: false')
      }
    } catch (error: any) {
      const errorInfo = handleFirebaseError(error)
      setDashboardState({
        data: null,
        loading: false,
        error: errorInfo.message
      })
      throw error
    }
  }, [])

  // 移除設備
  const removeDevice = useCallback(async (deviceId: string, reason = 'user_request') => {
    try {
      console.log('🗑️ Removing device:', deviceId)
      
      const result = await firebaseApi.removeDevice({
        device_id: deviceId,
        reason: reason
      })
      
      if (result.success) {
        console.log('✅ Device removed successfully:', result.data)
        
        // 自動刷新 Dashboard 數據
        await loadDashboard()
        
        return result.data
      } else {
        throw new Error('Remove device failed')
      }
    } catch (error: any) {
      const errorInfo = handleFirebaseError(error)
      console.error('❌ Remove device failed:', errorInfo.message)
      throw new Error(errorInfo.message)
    }
  }, [loadDashboard])

  // 創建或更新用戶
  const createOrUpdateUser = useCallback(async (userData: {
    email: string
    display_name?: string
    profile_image?: string
    preferences?: Record<string, any>
  }) => {
    try {
      console.log('👤 Creating/updating user:', userData)
      
      const result = await firebaseApi.createOrUpdateUser(userData)
      console.log('✅ User created/updated successfully:', result)
      
      return result
    } catch (error: any) {
      const errorInfo = handleFirebaseError(error)
      console.error('❌ Create/update user failed:', errorInfo.message)
      throw new Error(errorInfo.message)
    }
  }, [])

  // 驗證或註冊設備
  const validateOrRegisterDevice = useCallback(async (deviceData: {
    device_name: string
    platform: string
    app_version?: string
    device_info?: Record<string, any>
  }) => {
    try {
      console.log('📱 Validating/registering device:', deviceData)
      
      const result = await firebaseApi.validateOrRegisterDevice(deviceData)
      console.log('✅ Device validated/registered successfully:', result)
      
      return result
    } catch (error: any) {
      const errorInfo = handleFirebaseError(error)
      console.error('❌ Validate/register device failed:', errorInfo.message)
      throw new Error(errorInfo.message)
    }
  }, [])

  // 檢查使用量
  const checkUsage = useCallback(async (estimatedDurationSeconds: number) => {
    try {
      console.log('📊 Checking usage for duration:', estimatedDurationSeconds)
      
      const result = await firebaseApi.checkUsageBeforeRecording({
        estimated_duration_seconds: estimatedDurationSeconds
      })
      
      console.log('✅ Usage check successful:', result)
      return result
    } catch (error: any) {
      const errorInfo = handleFirebaseError(error)
      console.error('❌ Usage check failed:', errorInfo.message)
      throw new Error(errorInfo.message)
    }
  }, [])

  // 提交使用量
  const submitUsage = useCallback(async (usageData: {
    session_id: string
    actual_duration_seconds: number
    transcript?: string
    audio_quality_score?: number
    processing_time_ms?: number
  }) => {
    try {
      console.log('📈 Submitting usage:', usageData)
      
      const result = await firebaseApi.submitUsage(usageData)
      console.log('✅ Usage submitted successfully:', result)
      
      // 自動刷新 Dashboard 數據以更新使用量
      await loadDashboard()
      
      return result
    } catch (error: any) {
      const errorInfo = handleFirebaseError(error)
      console.error('❌ Submit usage failed:', errorInfo.message)
      throw new Error(errorInfo.message)
    }
  }, [loadDashboard])

  // 創建訂閱（用於 Stripe Webhook）
  const createSubscription = useCallback(async (subscriptionData: {
    stripe_customer_id: string
    stripe_subscription_id: string
    plan_id: string
    status: string
    current_period_start: string
    current_period_end: string
    amount_paid: number
    payment_status: string
    period_start: string
    period_end: string
  }) => {
    try {
      console.log('💳 Creating subscription:', subscriptionData)
      
      const result = await firebaseApi.createSubscription(subscriptionData)
      console.log('✅ Subscription created successfully:', result)
      
      return result
    } catch (error: any) {
      const errorInfo = handleFirebaseError(error)
      console.error('❌ Create subscription failed:', errorInfo.message)
      throw new Error(errorInfo.message)
    }
  }, [])

  return {
    // 狀態
    dashboard: dashboardState,
    
    // API 方法
    loadDashboard,
    removeDevice,
    createOrUpdateUser,
    validateOrRegisterDevice,
    checkUsage,
    submitUsage,
    createSubscription,
    
    // 輔助方法
    refreshDashboard: loadDashboard,
    clearError: () => setDashboardState(prev => ({ ...prev, error: null }))
  }
}

// 專門用於 Dashboard 的 Hook
export const useDashboard = () => {
  const { dashboard, loadDashboard, refreshDashboard, clearError } = useFirebaseApi()
  
  return {
    data: dashboard.data,
    loading: dashboard.loading,
    error: dashboard.error,
    loadDashboard,
    refreshDashboard,
    clearError
  }
}

// 專門用於設備管理的 Hook
export const useDeviceManagement = () => {
  const { removeDevice, validateOrRegisterDevice, refreshDashboard } = useFirebaseApi()
  
  return {
    removeDevice,
    validateOrRegisterDevice,
    refreshDashboard
  }
}

// 專門用於使用量管理的 Hook
export const useUsageManagement = () => {
  const { checkUsage, submitUsage, refreshDashboard } = useFirebaseApi()
  
  return {
    checkUsage,
    submitUsage,
    refreshDashboard
  }
}
