import Stripe from 'stripe'
import { SUBSCRIPTION_PLANS, SubscriptionPlan, getPriceId as getClientPriceId, getPriceAmount as getClientPriceAmount, getYearlySavingsPercentage as getClientYearlySavingsPercentage } from './subscription-plans'

// Server-side Stripe instance (only use on server)
export const stripe = typeof window === 'undefined'
  ? new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-05-28.basil',
      typescript: true,
    })
  : null

// Re-export types and functions for backward compatibility
export type { SubscriptionPlan }
export { SUBSCRIPTION_PLANS }
export const getYearlySavingsPercentage = getClientYearlySavingsPercentage
export const getPriceId = getClientPriceId
export const getPriceAmount = getClientPriceAmount

// Helper function to map Stripe price IDs to plan names (for webhooks)
export function getPlanFromPriceId(priceId: string): string | null {
  for (const [planKey, planConfig] of Object.entries(SUBSCRIPTION_PLANS)) {
    if (planKey === 'FREE') continue

    // Type assertion since we know non-FREE plans have price IDs
    const paidPlan = planConfig as any
    if (paidPlan.monthlyPriceId === priceId || paidPlan.yearlyPriceId === priceId) {
      return planKey
    }
  }
  return null
}

// Legacy PRODUCTS export for backward compatibility
export const PRODUCTS = {
  FREE: SUBSCRIPTION_PLANS.FREE,
  STARTER: SUBSCRIPTION_PLANS.STARTER,
  PRO: SUBSCRIPTION_PLANS.PRO,
  PREMIUM: SUBSCRIPTION_PLANS.PREMIUM,
  MAX: SUBSCRIPTION_PLANS.MAX
}
