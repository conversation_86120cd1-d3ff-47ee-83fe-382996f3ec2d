// API 測試腳本 - 根據後端團隊指南
// 使用方法：在瀏覽器控制台中運行

const BACKEND_API_URL = "https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net";

// 獲取 Firebase Auth Token
const getFirebaseToken = async () => {
  // 檢查 Firebase Auth 是否可用
  if (typeof firebase === 'undefined' || !firebase.auth) {
    throw new Error('Firebase Auth 未初始化');
  }
  
  const user = firebase.auth().currentUser;
  
  if (!user) {
    throw new Error('用戶未登入');
  }
  
  console.log('👤 當前用戶:', {
    uid: user.uid,
    email: user.email,
    emailVerified: user.emailVerified,
    displayName: user.displayName
  });
  
  // 獲取 ID Token
  const token = await user.getIdToken();
  console.log('🎫 Token 獲取成功, 長度:', token.length);
  console.log('🎫 Token 預覽:', token.substring(0, 50) + '...');
  
  return token;
};

// 測試 get_user_dashboard API
const testGetUserDashboard = async () => {
  try {
    console.log('📊 測試 get_user_dashboard...');
    
    const token = await getFirebaseToken();
    
    const response = await fetch(`${BACKEND_API_URL}/get_user_dashboard`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
      // 不發送請求體
    });
    
    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ get_user_dashboard 成功:', data);
      return data;
    } else {
      const errorText = await response.text();
      console.error('❌ get_user_dashboard 失敗:', {
        status: response.status,
        statusText: response.statusText,
        errorText: errorText
      });
      return null;
    }
    
  } catch (error) {
    console.error('🚨 get_user_dashboard 測試失敗:', error);
    return null;
  }
};

// 測試 remove_device API
const testRemoveDevice = async (deviceId = 'test-device-id') => {
  try {
    console.log('🗑️ 測試 remove_device...');
    
    const token = await getFirebaseToken();
    
    const requestBody = {
      device_id: deviceId,
      reason: 'test'
    };
    
    console.log('📤 Request body:', requestBody);
    
    const response = await fetch(`${BACKEND_API_URL}/remove_device`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ remove_device 成功:', data);
      return data;
    } else {
      const errorText = await response.text();
      console.error('❌ remove_device 失敗:', {
        status: response.status,
        statusText: response.statusText,
        errorText: errorText
      });
      return null;
    }
    
  } catch (error) {
    console.error('🚨 remove_device 測試失敗:', error);
    return null;
  }
};

// 完整的 API 測試
const runFullAPITest = async () => {
  console.log('🚀 開始完整 API 測試...');
  console.log('🔗 Backend URL:', BACKEND_API_URL);
  
  try {
    // 1. 檢查用戶登入狀態
    console.log('\n1️⃣ 檢查用戶登入狀態...');
    if (typeof firebase === 'undefined' || !firebase.auth) {
      console.error('❌ Firebase Auth 未初始化');
      return;
    }
    
    const user = firebase.auth().currentUser;
    if (!user) {
      console.error('❌ 用戶未登入，請先登入');
      return;
    }
    
    console.log('✅ 用戶已登入:', user.email);
    
    // 2. 測試 get_user_dashboard
    console.log('\n2️⃣ 測試 get_user_dashboard...');
    const dashboardData = await testGetUserDashboard();
    
    // 3. 測試 remove_device（如果有設備）
    console.log('\n3️⃣ 測試 remove_device...');
    if (dashboardData && dashboardData.data && dashboardData.data.devices && dashboardData.data.devices.active_devices.length > 0) {
      const firstDevice = dashboardData.data.devices.active_devices[0];
      console.log('📱 使用第一個設備進行測試:', firstDevice.device_id);
      await testRemoveDevice(firstDevice.device_id);
    } else {
      console.log('📱 沒有活躍設備，使用測試設備 ID');
      await testRemoveDevice('test-device-id');
    }
    
    console.log('\n🎉 API 測試完成！');
    
  } catch (error) {
    console.error('🚨 完整測試失敗:', error);
  }
};

// 檢查 Firebase Auth 狀態
const checkFirebaseAuth = () => {
  console.log('🔍 檢查 Firebase Auth 狀態...');
  
  if (typeof firebase === 'undefined') {
    console.error('❌ Firebase 未載入');
    return false;
  }
  
  if (!firebase.auth) {
    console.error('❌ Firebase Auth 未初始化');
    return false;
  }
  
  const user = firebase.auth().currentUser;
  if (!user) {
    console.error('❌ 用戶未登入');
    console.log('💡 請先登入，然後重新運行測試');
    return false;
  }
  
  console.log('✅ Firebase Auth 狀態正常');
  console.log('👤 當前用戶:', {
    uid: user.uid,
    email: user.email,
    emailVerified: user.emailVerified
  });
  
  return true;
};

// 導出函數供控制台使用
window.testAPI = {
  checkFirebaseAuth,
  getFirebaseToken,
  testGetUserDashboard,
  testRemoveDevice,
  runFullAPITest
};

console.log('🧪 API 測試腳本已載入！');
console.log('📋 可用的測試函數:');
console.log('  - testAPI.checkFirebaseAuth() - 檢查 Firebase Auth 狀態');
console.log('  - testAPI.testGetUserDashboard() - 測試 get_user_dashboard');
console.log('  - testAPI.testRemoveDevice(deviceId) - 測試 remove_device');
console.log('  - testAPI.runFullAPITest() - 運行完整測試');
console.log('');
console.log('💡 使用方法: 在瀏覽器控制台中運行 testAPI.runFullAPITest()');
