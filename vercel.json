{"framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"NEXTAUTH_URL": "@nextauth_url", "NEXTAUTH_SECRET": "@nextauth_secret", "FIREBASE_PROJECT_ID": "@firebase_project_id", "FIREBASE_CLIENT_EMAIL": "@firebase_client_email", "FIREBASE_PRIVATE_KEY": "@firebase_private_key", "NEXT_PUBLIC_FIREBASE_API_KEY": "@next_public_firebase_api_key", "NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN": "@next_public_firebase_auth_domain", "NEXT_PUBLIC_FIREBASE_PROJECT_ID": "@next_public_firebase_project_id", "NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET": "@next_public_firebase_storage_bucket", "NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID": "@next_public_firebase_messaging_sender_id", "NEXT_PUBLIC_FIREBASE_APP_ID": "@next_public_firebase_app_id", "STRIPE_SECRET_KEY": "@stripe_secret_key", "STRIPE_WEBHOOK_SECRET": "@stripe_webhook_secret", "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY": "@next_public_stripe_publishable_key", "STRIPE_PRICE_ID_STARTER_MONTHLY": "@stripe_price_id_starter_monthly", "STRIPE_PRICE_ID_STARTER_YEARLY": "@stripe_price_id_starter_yearly", "STRIPE_PRICE_ID_PRO_MONTHLY": "@stripe_price_id_pro_monthly", "STRIPE_PRICE_ID_PRO_YEARLY": "@stripe_price_id_pro_yearly", "STRIPE_PRICE_ID_PREMIUM_MONTHLY": "@stripe_price_id_premium_monthly", "STRIPE_PRICE_ID_PREMIUM_YEARLY": "@stripe_price_id_premium_yearly", "STRIPE_PRICE_ID_MAX_MONTHLY": "@stripe_price_id_max_monthly", "STRIPE_PRICE_ID_MAX_YEARLY": "@stripe_price_id_max_yearly"}, "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}