{"name": "speakoneai-functions", "version": "1.0.0", "description": "Firebase Functions for SpeakOneAI Web", "main": "lib/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^6.4.0", "stripe": "^14.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "private": true}