#!/usr/bin/env node

/**
 * Environment Manager Script
 * 
 * This script helps manage multiple Firebase environments (dev/prod)
 * and automatically switches between them.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const ENVIRONMENTS = {
  dev: {
    name: 'development',
    envFile: '.env.development',
    firebaseProject: 'speakoneai-dev',
    description: 'Development environment with test data'
  },
  prod: {
    name: 'production',
    envFile: '.env.production',
    firebaseProject: 'speakoneai-prod',
    description: 'Production environment with live data'
  }
};

function showUsage() {
  console.log(`
🔧 Environment Manager for SpeakOneAI

Usage:
  node scripts/env-manager.js <command> [environment]

Commands:
  switch <env>    Switch to environment (dev/prod)
  status          Show current environment status
  list            List all available environments
  help            Show this help message

Examples:
  node scripts/env-manager.js switch dev
  node scripts/env-manager.js switch prod
  node scripts/env-manager.js status
`);
}

function getCurrentEnvironment() {
  try {
    const envLocalPath = path.join(process.cwd(), '.env.local');
    if (!fs.existsSync(envLocalPath)) {
      return null;
    }
    
    const envContent = fs.readFileSync(envLocalPath, 'utf8');
    
    // Check which project ID is being used
    const projectIdMatch = envContent.match(/NEXT_PUBLIC_FIREBASE_PROJECT_ID=(.+)/);
    if (projectIdMatch) {
      const projectId = projectIdMatch[1].replace(/"/g, '');
      if (projectId.includes('dev')) return 'dev';
      if (projectId.includes('prod')) return 'prod';
    }
    
    return null;
  } catch (error) {
    return null;
  }
}

function switchEnvironment(env) {
  if (!ENVIRONMENTS[env]) {
    console.error(`❌ Invalid environment: ${env}`);
    console.log('Available environments: dev, prod');
    process.exit(1);
  }

  const config = ENVIRONMENTS[env];
  const sourceFile = path.join(process.cwd(), config.envFile);
  const targetFile = path.join(process.cwd(), '.env.local');

  // Check if source environment file exists
  if (!fs.existsSync(sourceFile)) {
    console.error(`❌ Environment file not found: ${config.envFile}`);
    console.log('Please create the environment file first.');
    process.exit(1);
  }

  try {
    // Copy environment file
    console.log(`🔄 Switching to ${config.name} environment...`);
    fs.copyFileSync(sourceFile, targetFile);
    
    // Switch Firebase project
    console.log(`🔥 Setting Firebase project to ${config.firebaseProject}...`);
    try {
      execSync(`firebase use ${config.firebaseProject}`, { stdio: 'inherit' });
    } catch (error) {
      console.warn(`⚠️  Could not switch Firebase project. Make sure Firebase CLI is installed and you're logged in.`);
    }
    
    console.log(`✅ Successfully switched to ${config.name} environment`);
    console.log(`📝 Description: ${config.description}`);
    console.log(`📁 Environment file: ${config.envFile} → .env.local`);
    console.log(`🔥 Firebase project: ${config.firebaseProject}`);
    
  } catch (error) {
    console.error(`❌ Failed to switch environment: ${error.message}`);
    process.exit(1);
  }
}

function showStatus() {
  const currentEnv = getCurrentEnvironment();
  
  console.log('🔍 Current Environment Status\n');
  
  if (currentEnv) {
    const config = ENVIRONMENTS[currentEnv];
    console.log(`✅ Active Environment: ${config.name}`);
    console.log(`📝 Description: ${config.description}`);
    console.log(`📁 Source file: ${config.envFile}`);
    console.log(`🔥 Firebase project: ${config.firebaseProject}`);
  } else {
    console.log('❌ No environment detected or .env.local not found');
    console.log('Run "node scripts/env-manager.js switch <env>" to set up an environment');
  }
  
  console.log('\n📋 Available environments:');
  Object.entries(ENVIRONMENTS).forEach(([key, config]) => {
    const isActive = currentEnv === key ? '(active)' : '';
    console.log(`  ${key}: ${config.description} ${isActive}`);
  });
}

function listEnvironments() {
  console.log('📋 Available Environments\n');
  
  Object.entries(ENVIRONMENTS).forEach(([key, config]) => {
    const exists = fs.existsSync(path.join(process.cwd(), config.envFile)) ? '✅' : '❌';
    console.log(`${exists} ${key}:`);
    console.log(`   Name: ${config.name}`);
    console.log(`   File: ${config.envFile}`);
    console.log(`   Firebase: ${config.firebaseProject}`);
    console.log(`   Description: ${config.description}\n`);
  });
}

// Main execution
const command = process.argv[2];
const environment = process.argv[3];

switch (command) {
  case 'switch':
    if (!environment) {
      console.error('❌ Please specify an environment (dev/prod)');
      showUsage();
      process.exit(1);
    }
    switchEnvironment(environment);
    break;
    
  case 'status':
    showStatus();
    break;
    
  case 'list':
    listEnvironments();
    break;
    
  case 'help':
  case '--help':
  case '-h':
    showUsage();
    break;
    
  default:
    console.error(`❌ Unknown command: ${command || 'none'}`);
    showUsage();
    process.exit(1);
}
