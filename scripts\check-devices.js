const admin = require('firebase-admin');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: 'speakoneai-dev-9f995',
      clientEmail: '<EMAIL>',
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n') || ''
    })
  });
}

const db = admin.firestore();

async function checkDevices() {
  try {
    console.log('🔍 Checking devices collection...');
    
    const devicesSnapshot = await db.collection('devices').get();
    
    if (devicesSnapshot.empty) {
      console.log('❌ No devices found in Firestore');
      return;
    }
    
    console.log(`📱 Found ${devicesSnapshot.size} devices:`);
    
    devicesSnapshot.forEach(doc => {
      const data = doc.data();
      console.log(`\n📱 Device ID: ${doc.id}`);
      console.log(`   User ID: ${data.userId}`);
      console.log(`   Device Name: ${data.deviceName}`);
      console.log(`   Platform: ${data.platform}`);
      console.log(`   Is Active: ${data.isActive}`);
      console.log(`   Last Active: ${data.lastActiveAt?.toDate?.() || data.lastActiveAt}`);
      console.log(`   Created At: ${data.createdAt?.toDate?.() || data.createdAt}`);
    });
    
    // Check for a specific user
    console.log('\n🔍 Checking devices for user: <EMAIL>');
    
    const usersSnapshot = await db.collection('users').where('email', '==', '<EMAIL>').get();
    
    if (!usersSnapshot.empty) {
      const userId = usersSnapshot.docs[0].id;
      console.log(`👤 Found user ID: ${userId}`);
      
      const userDevicesSnapshot = await db.collection('devices').where('userId', '==', userId).get();
      
      if (userDevicesSnapshot.empty) {
        console.log('❌ No devices found for this user');
      } else {
        console.log(`📱 Found ${userDevicesSnapshot.size} devices for this user:`);
        userDevicesSnapshot.forEach(doc => {
          const data = doc.data();
          console.log(`   - ${data.deviceName} (${data.platform}) - Active: ${data.isActive}`);
        });
      }
    } else {
      console.log('❌ User not found');
    }
    
  } catch (error) {
    console.error('❌ Error checking devices:', error);
  }
}

checkDevices().then(() => {
  console.log('\n✅ Device check completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
