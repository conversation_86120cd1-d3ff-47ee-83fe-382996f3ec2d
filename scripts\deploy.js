#!/usr/bin/env node

/**
 * Deployment Script for SpeakOneAI
 * 
 * This script handles deployment to different environments (development/production)
 * and manages environment-specific configurations.
 * 
 * Usage: 
 *   npm run deploy:dev    # Deploy to development
 *   npm run deploy:prod   # Deploy to production
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Get environment from command line arguments
const environment = process.argv[2] || 'development';

if (!['development', 'production'].includes(environment)) {
  console.error('❌ Invalid environment. Use "development" or "production"');
  process.exit(1);
}

console.log(`🚀 Deploying to ${environment} environment...\n`);

/**
 * Copy environment-specific configuration
 */
function setupEnvironment() {
  const envFile = `.env.${environment}`;
  const targetFile = '.env.local';

  if (!fs.existsSync(envFile)) {
    console.error(`❌ Environment file ${envFile} not found`);
    console.log(`Please create ${envFile} with your ${environment} configuration`);
    process.exit(1);
  }

  // Copy environment file
  fs.copyFileSync(envFile, targetFile);
  console.log(`✅ Copied ${envFile} to ${targetFile}`);
}

/**
 * Build the application
 */
function buildApplication() {
  console.log('📦 Building application...');
  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ Build completed successfully');
  } catch (error) {
    console.error('❌ Build failed');
    process.exit(1);
  }
}

/**
 * Deploy to Firebase
 */
function deployToFirebase() {
  console.log('🔥 Deploying to Firebase...');
  
  try {
    // Set Firebase project based on environment
    const projectId = environment === 'production' ? 'speakoneai-prod' : 'speakoneai-dev';
    
    // Use the project from .firebaserc or set it explicitly
    execSync(`firebase use ${projectId}`, { stdio: 'inherit' });
    
    // Deploy hosting and functions
    execSync('firebase deploy --only hosting,functions', { stdio: 'inherit' });
    
    console.log('✅ Firebase deployment completed successfully');
  } catch (error) {
    console.error('❌ Firebase deployment failed');
    process.exit(1);
  }
}

/**
 * Validate environment configuration
 */
function validateEnvironment() {
  const requiredVars = [
    'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
    'STRIPE_SECRET_KEY',
    'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY'
  ];

  const envFile = `.env.${environment}`;
  const envContent = fs.readFileSync(envFile, 'utf8');

  const missingVars = requiredVars.filter(varName => {
    return !envContent.includes(varName) || envContent.includes(`${varName}=your-`);
  });

  if (missingVars.length > 0) {
    console.error('❌ Missing or incomplete environment variables:');
    missingVars.forEach(varName => console.error(`   - ${varName}`));
    console.log(`\nPlease update ${envFile} with your actual values`);
    process.exit(1);
  }

  console.log('✅ Environment validation passed');
}

/**
 * Main deployment process
 */
async function main() {
  try {
    console.log(`Environment: ${environment}`);
    console.log(`Timestamp: ${new Date().toISOString()}\n`);

    // Step 1: Validate environment
    validateEnvironment();

    // Step 2: Setup environment
    setupEnvironment();

    // Step 3: Build application
    buildApplication();

    // Step 4: Deploy to Firebase
    deployToFirebase();

    console.log('\n🎉 Deployment completed successfully!');
    
    if (environment === 'production') {
      console.log('🌐 Your app is now live at your production URL');
    } else {
      console.log('🔧 Your app is deployed to the development environment');
    }

  } catch (error) {
    console.error('\n❌ Deployment failed:', error.message);
    process.exit(1);
  }
}

// Run the deployment
if (require.main === module) {
  main();
}

module.exports = { main };
