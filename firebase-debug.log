[debug] [2025-07-28T03:10:19.907Z] ----------------------------------------------------------------------
[debug] [2025-07-28T03:10:19.909Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js deploy --project speakoneai-dev-9f995
[debug] [2025-07-28T03:10:19.909Z] CLI Version:   14.11.1
[debug] [2025-07-28T03:10:19.910Z] Platform:      win32
[debug] [2025-07-28T03:10:19.910Z] Node Version:  v22.11.0
[debug] [2025-07-28T03:10:19.910Z] Time:          Mon Jul 28 2025 11:10:19 GMT+0800 (China Standard Time)
[debug] [2025-07-28T03:10:19.910Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-28T03:10:20.053Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-28T03:10:20.054Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-28T03:10:20.054Z] [iam] checking project speakoneai-dev-9f995 for permissions ["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","datastore.indexes.create","datastore.indexes.delete","datastore.indexes.list","datastore.indexes.update","firebase.projects.get","firebasehosting.sites.update"]
[debug] [2025-07-28T03:10:20.055Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:10:20.055Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:10:20.056Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995:testIamPermissions [none]
[debug] [2025-07-28T03:10:20.056Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995:testIamPermissions x-goog-quota-user=projects/speakoneai-dev-9f995
[debug] [2025-07-28T03:10:20.056Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","datastore.indexes.create","datastore.indexes.delete","datastore.indexes.list","datastore.indexes.update","firebase.projects.get","firebasehosting.sites.update"]}
[debug] [2025-07-28T03:10:21.446Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995:testIamPermissions 200
[debug] [2025-07-28T03:10:21.447Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","datastore.indexes.create","datastore.indexes.delete","datastore.indexes.list","datastore.indexes.update","firebase.projects.get","firebasehosting.sites.update"]}
[debug] [2025-07-28T03:10:21.447Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:10:21.447Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:10:21.447Z] >>> [apiv2][query] POST https://iam.googleapis.com/v1/projects/speakoneai-dev-9f995/serviceAccounts/<EMAIL>:testIamPermissions [none]
[debug] [2025-07-28T03:10:21.447Z] >>> [apiv2][body] POST https://iam.googleapis.com/v1/projects/speakoneai-dev-9f995/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[debug] [2025-07-28T03:10:23.031Z] <<< [apiv2][status] POST https://iam.googleapis.com/v1/projects/speakoneai-dev-9f995/serviceAccounts/<EMAIL>:testIamPermissions 200
[debug] [2025-07-28T03:10:23.031Z] <<< [apiv2][body] POST https://iam.googleapis.com/v1/projects/speakoneai-dev-9f995/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[debug] [2025-07-28T03:10:23.032Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:10:23.032Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:10:23.032Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/speakoneai-dev-9f995 [none]
[debug] [2025-07-28T03:10:23.564Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/speakoneai-dev-9f995 200
[debug] [2025-07-28T03:10:23.565Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/speakoneai-dev-9f995 {"projectId":"speakoneai-dev-9f995","projectNumber":"************","displayName":"speakoneai-dev","name":"projects/speakoneai-dev-9f995","resources":{"hostingSite":"speakoneai-dev-9f995"},"state":"ACTIVE","etag":"1_24466b59-921b-4078-9d57-28a983d87e42"}
[debug] [2025-07-28T03:10:25.685Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\hugo102578_gmail_com_application_default_credentials.json
[debug] [2025-07-28T03:10:25.686Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:10:25.686Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:10:25.686Z] >>> [apiv2][query] GET https://firebasehosting.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/sites pageToken=&pageSize=10
[debug] [2025-07-28T03:10:26.469Z] <<< [apiv2][status] GET https://firebasehosting.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/sites 200
[debug] [2025-07-28T03:10:26.469Z] <<< [apiv2][body] GET https://firebasehosting.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/sites {"sites":[{"name":"projects/speakoneai-dev-9f995/sites/speakoneai-dev-9f995","defaultUrl":"https://speakoneai-dev-9f995.web.app","type":"DEFAULT_SITE"}]}
[debug] [2025-07-28T03:10:26.470Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:10:26.470Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:10:26.470Z] >>> [apiv2][query] GET https://firebasehosting.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/sites 
[debug] [2025-07-28T03:10:26.987Z] <<< [apiv2][status] GET https://firebasehosting.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/sites 200
[debug] [2025-07-28T03:10:26.987Z] <<< [apiv2][body] GET https://firebasehosting.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/sites {"sites":[{"name":"projects/speakoneai-dev-9f995/sites/speakoneai-dev-9f995","defaultUrl":"https://speakoneai-dev-9f995.web.app","type":"DEFAULT_SITE"}]}
[debug] [2025-07-28T03:10:26.988Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:10:26.988Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:10:26.988Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/webApps/-/config [none]
[debug] [2025-07-28T03:10:27.569Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/webApps/-/config 200
[debug] [2025-07-28T03:10:27.569Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/webApps/-/config {"projectId":"speakoneai-dev-9f995","appId":"1:************:web:4d2d174bdc3aceb8a11fbe","storageBucket":"speakoneai-dev-9f995.firebasestorage.app","apiKey":"AIzaSyAHmnBBM3pVQTWWX5t5034_ffw0YxekNAo","authDomain":"speakoneai-dev-9f995.firebaseapp.com","messagingSenderId":"************"}
[info] 
   Thank you for trying our early preview of Next.js support on Firebase Hosting.
   During the preview, support is best-effort and breaking changes can be expected. Proceed with caution.
   The integration is known to work with Next.js version 12 - 15.0. You may encounter errors.

   Documentation: https://firebase.google.com/docs/hosting/frameworks/nextjs
   File a bug: https://github.com/firebase/firebase-tools/issues/new?template=bug_report.md
   Submit a feature request: https://github.com/firebase/firebase-tools/issues/new?template=feature_request.md

   We'd love to learn from you. Express your interest in helping us shape the future of Firebase Hosting: https://goo.gle/41enW5X

[debug] [2025-07-28T03:10:33.340Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:10:33.340Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:10:33.340Z] >>> [apiv2][query] GET https://firebasehosting.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/sites/speakoneai-dev-9f995 [none]
[debug] [2025-07-28T03:10:34.370Z] <<< [apiv2][status] GET https://firebasehosting.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/sites/speakoneai-dev-9f995 200
[debug] [2025-07-28T03:10:34.370Z] <<< [apiv2][body] GET https://firebasehosting.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/sites/speakoneai-dev-9f995 {"name":"projects/speakoneai-dev-9f995/sites/speakoneai-dev-9f995","defaultUrl":"https://speakoneai-dev-9f995.web.app","type":"DEFAULT_SITE"}
[info]    ▲ Next.js 15.3.4

[info]    - Environments: .env.local, .env


[info]    Creating an optimized production build ...

[info]  ✓ Compiled successfully in 2000ms

[info]    Skipping validation of types

[info]    Linting ...

[info] 
./src/app/api/checkout/route.ts
77:19  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/webhooks/stripe/route.ts
8:10  Warning: 'signInWithCustomToken' is defined but never used.  @typescript-eslint/no-unused-vars
9:10  Warning: 'auth' is defined but never used.  @typescript-eslint/no-unused-vars
90:63  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
91:61  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
116:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
151:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
178:63  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
179:61  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
202:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
268:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
277:79  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/page.tsx
15:3  Warning: 'ExternalLink' is defined but never used.  @typescript-eslint/no-unused-vars
18:3  Warning: 'Clock' is defined but never used.  @typescript-eslint/no-unused-vars
27:10  Warning: 'PRODUCTS' is defined but never used.  @typescript-eslint/no-unused-vars
50:21  Warning: 'setPurchases' is assigned a value but never used.  @typescript-eslint/no-unused-vars
51:19  Warning: 'setLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
57:50  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
61:33  Warning: 'formatTimeWithSeconds' is assigned a value but never used.  @typescript-eslint/no-unused-vars
68:8  Warning: React Hook useCallback has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
98:6  Warning: React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
125:52  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
174:61  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
195:48  Warning: '_productName' is defined but never used.  @typescript-eslint/no-unused-vars
546:21  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/app/pricing/page.tsx
68:10  Warning: 'loading' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/auth/SSOModal.tsx
41:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
60:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
79:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/layout/footer.tsx
11:15  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/layout/header.tsx
57:15  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
97:21  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
171:25  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/pricing/subscription-plans.tsx
68:53  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
69:96  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/ui/file-upload.tsx
29:3  Warning: 'maxSize' is defined but never used.  @typescript-eslint/no-unused-vars
30:3  Warning: 'showPreview' is assigned a value but never used.  @typescript-eslint/no-unused-vars
126:9  Warning: 'supportedFormats' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/hooks/useFirebaseApi.ts
13:65  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
36:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
67:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
79:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
88:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
100:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
109:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
127:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
152:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
179:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/auth.ts
60:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
74:19  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
129:19  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/db.ts
167:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/firebase-api.ts
30:35  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
61:24  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
87:32  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
107:32  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
278:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/firebase.ts
38:10  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
39:9  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
40:11  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
41:16  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/language-detection.ts
38:23  Warning: 'locale' is defined but never used.  @typescript-eslint/no-unused-vars

./src/lib/stripe.ts
25:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/app/api-reference/config/eslint#disabling-rules

[info]    Collecting page data ...

[info] ✅ Firebase initialized successfully for environment: production

[info] 🔥 Project ID: speakoneai-dev-9f995

[info]    Generating static pages (0/8) ...

[info] ✅ Firebase initialized successfully for environment: production
🔥 Project ID: speakoneai-dev-9f995

[info] ✅ Firebase initialized successfully for environment: production
🔥 Project ID: speakoneai-dev-9f995

[info]    Generating static pages (2/8) 

[info]    Generating static pages (4/8) 

[info]    Generating static pages (6/8) 
 ✓ Generating static pages (8/8)

[info]    Finalizing page optimization ...

[info]    Collecting build traces ...

[info] 

[info] Route (app)                                 Size  First Load JS
┌ ○ /                                       5 kB         270 kB
├ ○ /_not-found                            977 B         102 kB
├ ƒ /api/checkout                          138 B         101 kB
├ ƒ /api/webhooks/stripe                   138 B         101 kB
├ ○ /dashboard                           8.18 kB         265 kB
└ ○ /pricing                             4.25 kB         265 kB
+ First Load JS shared by all             101 kB
  ├ chunks/4bd1b696-7d52f47172210184.js  53.2 kB
  ├ chunks/684-5515cb541f008308.js       45.9 kB
  └ other shared chunks (total)          2.03 kB


[info] 
○  (Static)   prerendered as static content
ƒ  (Dynamic)  server-rendered on demand


[info] Building a Cloud Function to run this application. This is needed due to:
[info]  • non-static component /api/checkout/route
[info]  • non-static component /api/webhooks/stripe/route
[info] 
[debug] [2025-07-28T03:10:57.156Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:10:57.156Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:10:57.156Z] >>> [apiv2][query] GET https://firebasehosting.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/sites/speakoneai-dev-9f995 [none]
[debug] [2025-07-28T03:10:57.747Z] <<< [apiv2][status] GET https://firebasehosting.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/sites/speakoneai-dev-9f995 200
[debug] [2025-07-28T03:10:57.747Z] <<< [apiv2][body] GET https://firebasehosting.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/sites/speakoneai-dev-9f995 {"name":"projects/speakoneai-dev-9f995/sites/speakoneai-dev-9f995","defaultUrl":"https://speakoneai-dev-9f995.web.app","type":"DEFAULT_SITE"}
[warn] !  This integration expects Node version 16, 18, or 20. You're running version 22, problems may be encountered. 
[debug] [2025-07-28T03:10:59.711Z] [web frameworks] effective firebase.json:  {
  "hosting": [
    {
      "source": ".",
      "ignore": [
        "firebase.json",
        "**/.*",
        "**/node_modules/**"
      ],
      "frameworksBackend": {
        "region": "asia-east1"
      },
      "site": "speakoneai-dev-9f995",
      "rewrites": [
        {
          "source": "**",
          "function": {
            "functionId": "ssrspeakoneaidev9f995",
            "region": "asia-east1",
            "pinTag": true
          }
        }
      ],
      "redirects": [
        {
          "source": "/home",
          "destination": "/",
          "type": 308
        }
      ],
      "headers": [
        {
          "source": "/(.*)",
          "headers": [
            {
              "key": "X-Frame-Options",
              "value": "DENY"
            },
            {
              "key": "X-Content-Type-Options",
              "value": "nosniff"
            },
            {
              "key": "X-XSS-Protection",
              "value": "1; mode=block"
            },
            {
              "key": "Referrer-Policy",
              "value": "strict-origin-when-cross-origin"
            },
            {
              "key": "Permissions-Policy",
              "value": "camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()"
            },
            {
              "key": "Strict-Transport-Security",
              "value": "max-age=31536000; includeSubDomains; preload"
            },
            {
              "key": "X-DNS-Prefetch-Control",
              "value": "on"
            },
            {
              "key": "X-Robots-Tag",
              "value": "index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1"
            },
            {
              "key": "Content-Security-Policy",
              "value": "upgrade-insecure-requests"
            }
          ]
        },
        {
          "source": "/api/(.*)",
          "headers": [
            {
              "key": "Cache-Control",
              "value": "no-store, no-cache, must-revalidate, proxy-revalidate"
            },
            {
              "key": "X-Content-Type-Options",
              "value": "nosniff"
            },
            {
              "key": "X-Frame-Options",
              "value": "DENY"
            }
          ]
        },
        {
          "source": "/manifest.json",
          "headers": [
            {
              "key": "Cache-Control",
              "value": "public, max-age=31536000, immutable"
            }
          ]
        },
        {
          "source": "/favicon.ico",
          "headers": [
            {
              "key": "Cache-Control",
              "value": "public, max-age=31536000, immutable"
            }
          ]
        },
        {
          "source": "/images/:path*",
          "headers": [
            {
              "key": "Cache-Control",
              "value": "public, max-age=31536000, immutable"
            }
          ]
        },
        {
          "source": "/_not-found",
          "headers": [
            {
              "key": "x-nextjs-stale-time",
              "value": "300"
            },
            {
              "key": "x-nextjs-prerender",
              "value": "1"
            },
            {
              "key": "x-next-cache-tags",
              "value": "_N_T_/layout,_N_T_/_not-found/layout,_N_T_/_not-found/page,_N_T_/_not-found"
            }
          ]
        },
        {
          "source": "/pricing",
          "headers": [
            {
              "key": "x-nextjs-stale-time",
              "value": "300"
            },
            {
              "key": "x-nextjs-prerender",
              "value": "1"
            },
            {
              "key": "x-next-cache-tags",
              "value": "_N_T_/layout,_N_T_/pricing/layout,_N_T_/pricing/page,_N_T_/pricing"
            }
          ]
        },
        {
          "source": "/dashboard",
          "headers": [
            {
              "key": "x-nextjs-stale-time",
              "value": "300"
            },
            {
              "key": "x-nextjs-prerender",
              "value": "1"
            },
            {
              "key": "x-next-cache-tags",
              "value": "_N_T_/layout,_N_T_/dashboard/layout,_N_T_/dashboard/page,_N_T_/dashboard"
            }
          ]
        },
        {
          "source": "**/*.[jt]s",
          "headers": [
            {
              "key": "Set-Cookie",
              "value": "__FIREBASE_DEFAULTS__=eyJjb25maWciOnsicHJvamVjdElkIjoic3BlYWtvbmVhaS1kZXYtOWY5OTUiLCJhcHBJZCI6IjE6MzY4OTA2MzMxNDE5OndlYjo0ZDJkMTc0YmRjM2FjZWI4YTExZmJlIiwic3RvcmFnZUJ1Y2tldCI6InNwZWFrb25lYWktZGV2LTlmOTk1LmZpcmViYXNlc3RvcmFnZS5hcHAiLCJhcGlLZXkiOiJBSXphU3lBSG1uQkJNM3BWUVRXV1g1dDUwMzRfZmZ3MFl4ZWtOQW8iLCJhdXRoRG9tYWluIjoic3BlYWtvbmVhaS1kZXYtOWY5OTUuZmlyZWJhc2VhcHAuY29tIiwibWVzc2FnaW5nU2VuZGVySWQiOiIzNjg5MDYzMzE0MTkifSwiX2F1dGhUb2tlblN5bmNVUkwiOiIvX19zZXNzaW9uIn0; SameSite=Strict; Expires=2027-06-22T13:50:59.711Z; Path=/;"
            }
          ]
        }
      ],
      "cleanUrls": true,
      "trailingSlash": false,
      "public": ".firebase\\speakoneai-dev-9f995\\hosting",
      "webFramework": "next_ssr"
    }
  ],
  "functions": [
    {
      "source": "functions",
      "codebase": "default",
      "runtime": "nodejs18"
    },
    {
      "source": ".firebase\\speakoneai-dev-9f995\\functions",
      "codebase": "firebase-frameworks-speakoneai-dev-9f995"
    }
  ]
}
[info] 
[info] === Deploying to 'speakoneai-dev-9f995'...
[info] 
[info] i  deploying firestore, functions, hosting 
[info] i  firestore: reading indexes from firestore.indexes.json... 
[info] i  cloud.firestore: checking firestore.rules for compilation errors... 
[debug] [2025-07-28T03:10:59.714Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:10:59.714Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:10:59.714Z] >>> [apiv2][query] POST https://firebaserules.googleapis.com/v1/projects/speakoneai-dev-9f995:test [none]
[debug] [2025-07-28T03:10:59.714Z] >>> [apiv2][body] POST https://firebaserules.googleapis.com/v1/projects/speakoneai-dev-9f995:test [omitted]
[debug] [2025-07-28T03:11:00.817Z] <<< [apiv2][status] POST https://firebaserules.googleapis.com/v1/projects/speakoneai-dev-9f995:test 200
[debug] [2025-07-28T03:11:00.817Z] <<< [apiv2][body] POST https://firebaserules.googleapis.com/v1/projects/speakoneai-dev-9f995:test {}
[info] +  cloud.firestore: rules file firestore.rules compiled successfully 
[debug] [2025-07-28T03:11:00.819Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:00.819Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:00.819Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995 [none]
[debug] [2025-07-28T03:11:01.746Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995 200
[debug] [2025-07-28T03:11:01.746Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995 {"projectNumber":"************","projectId":"speakoneai-dev-9f995","lifecycleState":"ACTIVE","name":"speakoneai-dev","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-07-27T04:47:32.845060Z"}
[info] i  functions: preparing codebase default for deployment 
[info] i  functions: preparing codebase firebase-frameworks-speakoneai-dev-9f995 for deployment 
[info] i  functions: ensuring required API cloudfunctions.googleapis.com is enabled... 
[info] i  functions: ensuring required API cloudbuild.googleapis.com is enabled... 
[info] i  artifactregistry: ensuring required API artifactregistry.googleapis.com is enabled... 
[debug] [2025-07-28T03:11:01.749Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:01.749Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:01.749Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/adminSdkConfig [none]
[debug] [2025-07-28T03:11:02.099Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/adminSdkConfig 200
[debug] [2025-07-28T03:11:02.099Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/adminSdkConfig {"projectId":"speakoneai-dev-9f995","storageBucket":"speakoneai-dev-9f995.firebasestorage.app"}
[debug] [2025-07-28T03:11:02.100Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:02.100Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:02.100Z] >>> [apiv2][query] GET https://runtimeconfig.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/configs [none]
[debug] [2025-07-28T03:11:03.023Z] <<< [apiv2][status] GET https://runtimeconfig.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/configs 200
[debug] [2025-07-28T03:11:03.023Z] <<< [apiv2][body] GET https://runtimeconfig.googleapis.com/v1beta1/projects/speakoneai-dev-9f995/configs {}
[debug] [2025-07-28T03:11:03.024Z] Validating nodejs source
[warn] !  functions: Runtime Node.js 18 was deprecated on 2025-04-30 and will be decommissioned on 2025-10-30, after which you will not be able to deploy without upgrading. Consider upgrading now to avoid disruption. See https://cloud.google.com/functions/docs/runtime-support for full details on the lifecycle policy 
[warn] !  functions: package.json indicates an outdated version of firebase-functions. Please upgrade using npm install --save firebase-functions@latest in your functions directory. 
[warn] !  functions: Please note that there will be breaking changes when you upgrade. 
[debug] [2025-07-28T03:11:03.734Z] > [functions] package.json contents: {
  "name": "speakoneai-functions",
  "version": "1.0.0",
  "description": "Firebase Functions for SpeakOneAI Web",
  "main": "lib/index.js",
  "scripts": {
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "18"
  },
  "dependencies": {
    "firebase-admin": "^12.0.0",
    "firebase-functions": "^6.4.0",
    "stripe": "^14.0.0"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "typescript": "^5.0.0"
  },
  "private": true
}
[debug] [2025-07-28T03:11:03.734Z] Building nodejs source
[info] i  functions: Loading and analyzing source code for codebase default to determine what to deploy 
[debug] [2025-07-28T03:11:03.735Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-28T03:11:03.742Z] Found firebase-functions binary at 'C:\Local_Repos\SpeakOneAIWeb\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8522

[debug] [2025-07-28T03:11:04.522Z] Got response from /__/functions.yaml {"endpoints":{"handleStripeWebhook":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"handleStripeWebhook"},"logClientUsage":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"logClientUsage"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[debug] [2025-07-28T03:11:08.558Z] Validating nodejs source
[debug] [2025-07-28T03:11:09.305Z] > [functions] package.json contents: {
  "name": "speakoneai-web",
  "version": "1.0.0",
  "private": true,
  "description": "SpeakOneAI Web - Payment and download platform for Windows and MacOS client apps",
  "dependencies": {
    "@auth/firebase-adapter": "^2.10.0",
    "@headlessui/react": "^2.2.4",
    "@hookform/resolvers": "^5.1.1",
    "@radix-ui/react-label": "^2.1.7",
    "@radix-ui/react-select": "^2.2.5",
    "@radix-ui/react-slot": "^1.2.3",
    "@stripe/stripe-js": "^7.4.0",
    "autoprefixer": "^10.4.21",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "dotenv": "^16.6.1",
    "embla-carousel-react": "^8.6.0",
    "firebase": "^11.1.0",
    "firebase-admin": "^12.7.0",
    "lucide-react": "^0.525.0",
    "next": "15.3.4",
    "next-auth": "^4.24.11",
    "postcss": "^8.5.6",
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "react-hook-form": "^7.59.0",
    "stripe": "^18.2.1",
    "tailwind-merge": "^3.3.1",
    "tailwindcss": "^3.4.17",
    "zod": "^3.25.67",
    "firebase-frameworks": "^0.11.0",
    "firebase-functions": "^6.0.1"
  },
  "main": "server.js",
  "engines": {
    "node": "20"
  }
}
[debug] [2025-07-28T03:11:09.306Z] Building nodejs source
[info] i  functions: Loading and analyzing source code for codebase firebase-frameworks-speakoneai-dev-9f995 to determine what to deploy 
[debug] [2025-07-28T03:11:09.306Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-28T03:11:09.311Z] Found firebase-functions binary at 'C:\Local_Repos\SpeakOneAIWeb\.firebase\speakoneai-dev-9f995\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8388

[debug] [2025-07-28T03:11:09.747Z] Got response from /__/functions.yaml {"endpoints":{"ssrspeakoneaidev9f995":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","region":["asia-east1"],"labels":{},"httpsTrigger":{},"entryPoint":"ssrspeakoneaidev9f995"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] i  extensions: ensuring required API firebaseextensions.googleapis.com is enabled... 
[debug] [2025-07-28T03:11:13.773Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:13.773Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:13.773Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/speakoneai-dev-9f995/services/firebaseextensions.googleapis.com [none]
[debug] [2025-07-28T03:11:13.773Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/speakoneai-dev-9f995/services/firebaseextensions.googleapis.com x-goog-quota-user=projects/speakoneai-dev-9f995
[debug] [2025-07-28T03:11:15.160Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/speakoneai-dev-9f995/services/firebaseextensions.googleapis.com 200
[debug] [2025-07-28T03:11:15.160Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/speakoneai-dev-9f995/services/firebaseextensions.googleapis.com [omitted]
[warn] !  extensions: missing required API firebaseextensions.googleapis.com. Enabling now... 
[debug] [2025-07-28T03:11:15.161Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:15.161Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:15.161Z] >>> [apiv2][query] POST https://serviceusage.googleapis.com/v1/projects/speakoneai-dev-9f995/services/firebaseextensions.googleapis.com:enable [none]
[debug] [2025-07-28T03:11:15.161Z] >>> [apiv2][(partial)header] POST https://serviceusage.googleapis.com/v1/projects/speakoneai-dev-9f995/services/firebaseextensions.googleapis.com:enable x-goog-quota-user=projects/speakoneai-dev-9f995
[debug] [2025-07-28T03:11:15.638Z] <<< [apiv2][status] POST https://serviceusage.googleapis.com/v1/projects/speakoneai-dev-9f995/services/firebaseextensions.googleapis.com:enable 200
[debug] [2025-07-28T03:11:15.638Z] <<< [apiv2][body] POST https://serviceusage.googleapis.com/v1/projects/speakoneai-dev-9f995/services/firebaseextensions.googleapis.com:enable [omitted]
[debug] [2025-07-28T03:11:25.653Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-28T03:11:25.654Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-28T03:11:25.654Z] [iam] checking project speakoneai-dev-9f995 for permissions ["firebase.projects.get","firebaseextensions.instances.list"]
[debug] [2025-07-28T03:11:25.654Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:25.654Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:25.654Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995:testIamPermissions [none]
[debug] [2025-07-28T03:11:25.654Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995:testIamPermissions x-goog-quota-user=projects/speakoneai-dev-9f995
[debug] [2025-07-28T03:11:25.654Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995:testIamPermissions {"permissions":["firebase.projects.get","firebaseextensions.instances.list"]}
[debug] [2025-07-28T03:11:26.620Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995:testIamPermissions 200
[debug] [2025-07-28T03:11:26.620Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995:testIamPermissions {"permissions":["firebase.projects.get","firebaseextensions.instances.list"]}
[debug] [2025-07-28T03:11:26.620Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:26.620Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:26.621Z] >>> [apiv2][query] GET https://firebaseextensions.googleapis.com/v1beta/projects/speakoneai-dev-9f995/instances pageSize=100&pageToken=
[debug] [2025-07-28T03:11:29.194Z] <<< [apiv2][status] GET https://firebaseextensions.googleapis.com/v1beta/projects/speakoneai-dev-9f995/instances 200
[debug] [2025-07-28T03:11:29.194Z] <<< [apiv2][body] GET https://firebaseextensions.googleapis.com/v1beta/projects/speakoneai-dev-9f995/instances {}
[info] i  functions: Loaded environment variables from .env. 
[info] i  functions: preparing functions directory for uploading... 
[info] i  functions: packaged C:\Local_Repos\SpeakOneAIWeb\functions (35.42 KB) for uploading 
[info] i  functions: preparing .firebase\speakoneai-dev-9f995\functions directory for uploading... 
[info] i  functions: packaged C:\Local_Repos\SpeakOneAIWeb\.firebase\speakoneai-dev-9f995\functions (33.98 MB) for uploading 
[debug] [2025-07-28T03:11:31.081Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:31.081Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:31.081Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v1/projects/speakoneai-dev-9f995/locations/-/functions [none]
[debug] [2025-07-28T03:11:32.056Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v1/projects/speakoneai-dev-9f995/locations/-/functions 200
[debug] [2025-07-28T03:11:32.056Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v1/projects/speakoneai-dev-9f995/locations/-/functions {}
[debug] [2025-07-28T03:11:32.057Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:32.057Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:32.057Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/speakoneai-dev-9f995/locations/-/functions filter=environment%3D%22GEN_2%22
[debug] [2025-07-28T03:11:33.454Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/speakoneai-dev-9f995/locations/-/functions 200
[debug] [2025-07-28T03:11:33.455Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/speakoneai-dev-9f995/locations/-/functions {"functions":[{"name":"projects/speakoneai-dev-9f995/locations/asia-east1/functions/validate_or_register_device","buildConfig":{"build":"projects/************/locations/asia-east1/builds/51473dfc-68db-44ad-9767-25d9bc5d6cbf","runtime":"python311","entryPoint":"validate_or_register_device","source":{"storageSource":{"bucket":"gcf-v2-sources-************-asia-east1","object":"validate_or_register_device/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/speakoneai-dev-9f995/locations/asia-east1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-asia-east1","object":"validate_or_register_device/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/speakoneai-dev-9f995/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/speakoneai-dev-9f995/locations/asia-east1/services/validate-or-register-device","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"speakoneai-dev-9f995\",\"storageBucket\":\"speakoneai-dev-9f995.firebasestorage.app\"}","GCLOUD_PROJECT":"speakoneai-dev-9f995","EVENTARC_CLOUD_EVENT_SOURCE":"projects/speakoneai-dev-9f995/locations/asia-east1/services/validate_or_register_device","FUNCTION_TARGET":"validate_or_register_device","LOG_EXECUTION_ID":"true","FUNCTION_SIGNATURE_TYPE":"http"},"maxInstanceCount":20,"ingressSettings":"ALLOW_ALL","uri":"https://validate-or-register-device-eblm4v26dq-de.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"validate-or-register-device-00005-dej","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-07-27T14:37:20.224635366Z","labels":{"deployment-callable":"true","deployment-tool":"cli-firebase","firebase-functions-hash":"fe377e8c2e15c636f8c74a7def275d8220c694d7"},"environment":"GEN_2","url":"https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/validate_or_register_device","createTime":"2025-07-27T11:21:46.883228081Z","satisfiesPzi":true},{"name":"projects/speakoneai-dev-9f995/locations/asia-east1/functions/remove_device","buildConfig":{"build":"projects/************/locations/asia-east1/builds/51473dfc-68db-44ad-9767-25d9bc5d6cbf","runtime":"python311","entryPoint":"remove_device","source":{"storageSource":{"bucket":"gcf-v2-sources-************-asia-east1","object":"remove_device/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/speakoneai-dev-9f995/locations/asia-east1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-asia-east1","object":"remove_device/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/speakoneai-dev-9f995/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/speakoneai-dev-9f995/locations/asia-east1/services/remove-device","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"speakoneai-dev-9f995\",\"storageBucket\":\"speakoneai-dev-9f995.firebasestorage.app\"}","GCLOUD_PROJECT":"speakoneai-dev-9f995","EVENTARC_CLOUD_EVENT_SOURCE":"projects/speakoneai-dev-9f995/locations/asia-east1/services/remove_device","FUNCTION_TARGET":"remove_device","LOG_EXECUTION_ID":"true","FUNCTION_SIGNATURE_TYPE":"http"},"maxInstanceCount":20,"ingressSettings":"ALLOW_ALL","uri":"https://remove-device-eblm4v26dq-de.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"remove-device-00004-how","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-07-27T14:37:19.626189987Z","labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"fe377e8c2e15c636f8c74a7def275d8220c694d7","deployment-callable":"true"},"environment":"GEN_2","url":"https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/remove_device","createTime":"2025-07-27T13:20:10.012916764Z","satisfiesPzi":true},{"name":"projects/speakoneai-dev-9f995/locations/asia-east1/functions/calculate_usage_after_recording","buildConfig":{"build":"projects/************/locations/asia-east1/builds/51473dfc-68db-44ad-9767-25d9bc5d6cbf","runtime":"python311","entryPoint":"calculate_usage_after_recording","source":{"storageSource":{"bucket":"gcf-v2-sources-************-asia-east1","object":"calculate_usage_after_recording/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/speakoneai-dev-9f995/locations/asia-east1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-asia-east1","object":"calculate_usage_after_recording/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/speakoneai-dev-9f995/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/speakoneai-dev-9f995/locations/asia-east1/services/calculate-usage-after-recording","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"speakoneai-dev-9f995\",\"storageBucket\":\"speakoneai-dev-9f995.firebasestorage.app\"}","GCLOUD_PROJECT":"speakoneai-dev-9f995","EVENTARC_CLOUD_EVENT_SOURCE":"projects/speakoneai-dev-9f995/locations/asia-east1/services/calculate_usage_after_recording","FUNCTION_TARGET":"calculate_usage_after_recording","LOG_EXECUTION_ID":"true","FUNCTION_SIGNATURE_TYPE":"http"},"maxInstanceCount":20,"ingressSettings":"ALLOW_ALL","uri":"https://calculate-usage-after-recording-eblm4v26dq-de.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"calculate-usage-after-recording-00007-zob","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-07-27T14:37:16.295944615Z","labels":{"deployment-callable":"true","deployment-tool":"cli-firebase","firebase-functions-hash":"fe377e8c2e15c636f8c74a7def275d8220c694d7"},"environment":"GEN_2","url":"https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/calculate_usage_after_recording","createTime":"2025-07-27T06:35:32.859542993Z","satisfiesPzi":true},{"name":"projects/speakoneai-dev-9f995/locations/asia-east1/functions/submit_usage","buildConfig":{"build":"projects/************/locations/asia-east1/builds/fac920a1-7e8d-4d82-a518-c31946ac443e","runtime":"python311","entryPoint":"submit_usage","source":{"storageSource":{"bucket":"gcf-v2-sources-************-asia-east1","object":"submit_usage/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/speakoneai-dev-9f995/locations/asia-east1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-asia-east1","object":"submit_usage/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/speakoneai-dev-9f995/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/speakoneai-dev-9f995/locations/asia-east1/services/submit-usage","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"speakoneai-dev-9f995\",\"storageBucket\":\"speakoneai-dev-9f995.firebasestorage.app\"}","GCLOUD_PROJECT":"speakoneai-dev-9f995","EVENTARC_CLOUD_EVENT_SOURCE":"projects/speakoneai-dev-9f995/locations/asia-east1/services/submit_usage","FUNCTION_TARGET":"submit_usage","LOG_EXECUTION_ID":"true","FUNCTION_SIGNATURE_TYPE":"http"},"maxInstanceCount":20,"ingressSettings":"ALLOW_ALL","uri":"https://submit-usage-eblm4v26dq-de.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"submit-usage-00009-ley","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-07-27T16:16:19.206668162Z","labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"a9dec2e2d1a21ff73d4f4c4dc9926d302667abdc","deployment-callable":"true"},"environment":"GEN_2","url":"https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/submit_usage","createTime":"2025-07-27T06:37:01.324403232Z","satisfiesPzi":true},{"name":"projects/speakoneai-dev-9f995/locations/asia-east1/functions/create_subscription","buildConfig":{"build":"projects/************/locations/asia-east1/builds/51473dfc-68db-44ad-9767-25d9bc5d6cbf","runtime":"python311","entryPoint":"create_subscription","source":{"storageSource":{"bucket":"gcf-v2-sources-************-asia-east1","object":"create_subscription/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/speakoneai-dev-9f995/locations/asia-east1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-asia-east1","object":"create_subscription/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/speakoneai-dev-9f995/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/speakoneai-dev-9f995/locations/asia-east1/services/create-subscription","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"speakoneai-dev-9f995\",\"storageBucket\":\"speakoneai-dev-9f995.firebasestorage.app\"}","GCLOUD_PROJECT":"speakoneai-dev-9f995","EVENTARC_CLOUD_EVENT_SOURCE":"projects/speakoneai-dev-9f995/locations/asia-east1/services/create_subscription","FUNCTION_TARGET":"create_subscription","LOG_EXECUTION_ID":"true","FUNCTION_SIGNATURE_TYPE":"http"},"maxInstanceCount":20,"ingressSettings":"ALLOW_ALL","uri":"https://create-subscription-eblm4v26dq-de.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"create-subscription-00007-low","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-07-27T14:37:21.862621684Z","labels":{"deployment-callable":"true","deployment-tool":"cli-firebase","firebase-functions-hash":"fe377e8c2e15c636f8c74a7def275d8220c694d7"},"environment":"GEN_2","url":"https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/create_subscription","createTime":"2025-07-27T06:37:01.243516633Z","satisfiesPzi":true},{"name":"projects/speakoneai-dev-9f995/locations/asia-east1/functions/check_usage_before_recording","buildConfig":{"build":"projects/************/locations/asia-east1/builds/51473dfc-68db-44ad-9767-25d9bc5d6cbf","runtime":"python311","entryPoint":"check_usage_before_recording","source":{"storageSource":{"bucket":"gcf-v2-sources-************-asia-east1","object":"check_usage_before_recording/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/speakoneai-dev-9f995/locations/asia-east1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-asia-east1","object":"check_usage_before_recording/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/speakoneai-dev-9f995/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/speakoneai-dev-9f995/locations/asia-east1/services/check-usage-before-recording","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"speakoneai-dev-9f995\",\"storageBucket\":\"speakoneai-dev-9f995.firebasestorage.app\"}","GCLOUD_PROJECT":"speakoneai-dev-9f995","EVENTARC_CLOUD_EVENT_SOURCE":"projects/speakoneai-dev-9f995/locations/asia-east1/services/check_usage_before_recording","FUNCTION_TARGET":"check_usage_before_recording","LOG_EXECUTION_ID":"true","FUNCTION_SIGNATURE_TYPE":"http"},"maxInstanceCount":20,"ingressSettings":"ALLOW_ALL","uri":"https://check-usage-before-recording-eblm4v26dq-de.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"check-usage-before-recording-00007-qon","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-07-27T14:37:19.939488170Z","labels":{"firebase-functions-hash":"fe377e8c2e15c636f8c74a7def275d8220c694d7","deployment-callable":"true","deployment-tool":"cli-firebase"},"environment":"GEN_2","url":"https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/check_usage_before_recording","createTime":"2025-07-27T06:37:01.136984626Z","satisfiesPzi":true},{"name":"projects/speakoneai-dev-9f995/locations/asia-east1/functions/create_or_update_user","buildConfig":{"build":"projects/************/locations/asia-east1/builds/51473dfc-68db-44ad-9767-25d9bc5d6cbf","runtime":"python311","entryPoint":"create_or_update_user","source":{"storageSource":{"bucket":"gcf-v2-sources-************-asia-east1","object":"create_or_update_user/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/speakoneai-dev-9f995/locations/asia-east1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-asia-east1","object":"create_or_update_user/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/speakoneai-dev-9f995/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/speakoneai-dev-9f995/locations/asia-east1/services/create-or-update-user","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"speakoneai-dev-9f995\",\"storageBucket\":\"speakoneai-dev-9f995.firebasestorage.app\"}","GCLOUD_PROJECT":"speakoneai-dev-9f995","EVENTARC_CLOUD_EVENT_SOURCE":"projects/speakoneai-dev-9f995/locations/asia-east1/services/create_or_update_user","FUNCTION_TARGET":"create_or_update_user","LOG_EXECUTION_ID":"true","FUNCTION_SIGNATURE_TYPE":"http"},"maxInstanceCount":20,"ingressSettings":"ALLOW_ALL","uri":"https://create-or-update-user-eblm4v26dq-de.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"create-or-update-user-00009-duw","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-07-27T14:37:20.472949411Z","labels":{"deployment-callable":"true","deployment-tool":"cli-firebase","firebase-functions-hash":"fe377e8c2e15c636f8c74a7def275d8220c694d7"},"environment":"GEN_2","url":"https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/create_or_update_user","createTime":"2025-07-27T06:37:01.309267395Z","satisfiesPzi":true},{"name":"projects/speakoneai-dev-9f995/locations/asia-east1/functions/get_user_dashboard","buildConfig":{"build":"projects/************/locations/asia-east1/builds/09ef4428-288c-470f-a788-8e9534e4a4ac","runtime":"python311","entryPoint":"get_user_dashboard","source":{"storageSource":{"bucket":"gcf-v2-sources-************-asia-east1","object":"get_user_dashboard/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/speakoneai-dev-9f995/locations/asia-east1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-asia-east1","object":"get_user_dashboard/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/speakoneai-dev-9f995/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/speakoneai-dev-9f995/locations/asia-east1/services/get-user-dashboard","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"speakoneai-dev-9f995\",\"storageBucket\":\"speakoneai-dev-9f995.firebasestorage.app\"}","GCLOUD_PROJECT":"speakoneai-dev-9f995","EVENTARC_CLOUD_EVENT_SOURCE":"projects/speakoneai-dev-9f995/locations/asia-east1/services/get_user_dashboard","FUNCTION_TARGET":"get_user_dashboard","LOG_EXECUTION_ID":"true","FUNCTION_SIGNATURE_TYPE":"http"},"maxInstanceCount":20,"ingressSettings":"ALLOW_ALL","uri":"https://get-user-dashboard-eblm4v26dq-de.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"get-user-dashboard-00006-wiy","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-07-27T15:28:16.405791994Z","labels":{"deployment-callable":"true","deployment-tool":"cli-firebase","firebase-functions-hash":"ed0060e277a3995227e7bee5369ae4e2e4b68a00"},"environment":"GEN_2","url":"https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/get_user_dashboard","createTime":"2025-07-27T11:20:39.002621463Z","satisfiesPzi":true}]}
[info] i  functions: ensuring required API run.googleapis.com is enabled... 
[debug] [2025-07-28T03:11:33.457Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:33.457Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: ensuring required API eventarc.googleapis.com is enabled... 
[debug] [2025-07-28T03:11:33.458Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:33.458Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: ensuring required API pubsub.googleapis.com is enabled... 
[debug] [2025-07-28T03:11:33.458Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:33.458Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: ensuring required API storage.googleapis.com is enabled... 
[debug] [2025-07-28T03:11:33.459Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:33.459Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:33.459Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/************/services/run.googleapis.com [none]
[debug] [2025-07-28T03:11:33.459Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/************/services/run.googleapis.com x-goog-quota-user=projects/************
[debug] [2025-07-28T03:11:33.460Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/************/services/eventarc.googleapis.com [none]
[debug] [2025-07-28T03:11:33.460Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/************/services/eventarc.googleapis.com x-goog-quota-user=projects/************
[debug] [2025-07-28T03:11:33.461Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/************/services/pubsub.googleapis.com [none]
[debug] [2025-07-28T03:11:33.461Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/************/services/pubsub.googleapis.com x-goog-quota-user=projects/************
[debug] [2025-07-28T03:11:33.462Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/************/services/storage.googleapis.com [none]
[debug] [2025-07-28T03:11:33.462Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/************/services/storage.googleapis.com x-goog-quota-user=projects/************
[debug] [2025-07-28T03:11:33.851Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/************/services/eventarc.googleapis.com 200
[debug] [2025-07-28T03:11:33.851Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/************/services/eventarc.googleapis.com [omitted]
[info] +  functions: required API eventarc.googleapis.com is enabled 
[debug] [2025-07-28T03:11:34.758Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/************/services/pubsub.googleapis.com 200
[debug] [2025-07-28T03:11:34.758Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/************/services/pubsub.googleapis.com [omitted]
[info] +  functions: required API pubsub.googleapis.com is enabled 
[debug] [2025-07-28T03:11:34.762Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/************/services/run.googleapis.com 200
[debug] [2025-07-28T03:11:34.762Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/************/services/run.googleapis.com [omitted]
[info] +  functions: required API run.googleapis.com is enabled 
[debug] [2025-07-28T03:11:34.767Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/************/services/storage.googleapis.com 200
[debug] [2025-07-28T03:11:34.767Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/************/services/storage.googleapis.com [omitted]
[info] +  functions: required API storage.googleapis.com is enabled 
[info] i  functions: generating the service identity for pubsub.googleapis.com... 
[debug] [2025-07-28T03:11:34.770Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:34.770Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: generating the service identity for eventarc.googleapis.com... 
[debug] [2025-07-28T03:11:34.770Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:34.771Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:34.771Z] >>> [apiv2][query] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/pubsub.googleapis.com:generateServiceIdentity [none]
[debug] [2025-07-28T03:11:34.771Z] >>> [apiv2][(partial)header] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/pubsub.googleapis.com:generateServiceIdentity x-goog-quota-user=projects/************
[debug] [2025-07-28T03:11:34.771Z] >>> [apiv2][body] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/pubsub.googleapis.com:generateServiceIdentity {}
[debug] [2025-07-28T03:11:34.771Z] >>> [apiv2][query] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/eventarc.googleapis.com:generateServiceIdentity [none]
[debug] [2025-07-28T03:11:34.771Z] >>> [apiv2][(partial)header] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/eventarc.googleapis.com:generateServiceIdentity x-goog-quota-user=projects/************
[debug] [2025-07-28T03:11:34.771Z] >>> [apiv2][body] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/eventarc.googleapis.com:generateServiceIdentity {}
[debug] [2025-07-28T03:11:35.087Z] <<< [apiv2][status] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/pubsub.googleapis.com:generateServiceIdentity 200
[debug] [2025-07-28T03:11:35.087Z] <<< [apiv2][body] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/pubsub.googleapis.com:generateServiceIdentity {"name":"operations/finished.DONE_OPERATION","done":true,"response":{"@type":"type.googleapis.com/google.api.serviceusage.v1beta1.ServiceIdentity","email":"<EMAIL>","uniqueId":"107560932683296624229"}}
[debug] [2025-07-28T03:11:35.108Z] <<< [apiv2][status] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/eventarc.googleapis.com:generateServiceIdentity 200
[debug] [2025-07-28T03:11:35.108Z] <<< [apiv2][body] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/eventarc.googleapis.com:generateServiceIdentity {"name":"operations/finished.DONE_OPERATION","done":true,"response":{"@type":"type.googleapis.com/google.api.serviceusage.v1beta1.ServiceIdentity","email":"<EMAIL>","uniqueId":"100716753927862688428"}}
[debug] [2025-07-28T03:11:35.110Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:35.110Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:35.110Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995 [none]
[debug] [2025-07-28T03:11:36.090Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995 200
[debug] [2025-07-28T03:11:36.090Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995 {"projectNumber":"************","projectId":"speakoneai-dev-9f995","lifecycleState":"ACTIVE","name":"speakoneai-dev","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-07-27T04:47:32.845060Z"}
[debug] [2025-07-28T03:11:36.090Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:36.090Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:36.090Z] >>> [apiv2][query] GET https://compute.googleapis.com/compute/v1/projects/************ [none]
[debug] [2025-07-28T03:11:36.631Z] <<< [apiv2][status] GET https://compute.googleapis.com/compute/v1/projects/************ 403
[debug] [2025-07-28T03:11:36.631Z] <<< [apiv2][body] GET https://compute.googleapis.com/compute/v1/projects/************ {"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"serviceTitle":"Compute Engine API","service":"compute.googleapis.com","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","containerInfo":"************","consumer":"projects/************"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}}
[debug] [2025-07-28T03:11:36.631Z] unable to look up default compute service account. Falling <NAME_EMAIL>. Error: {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"serviceTitle":"Compute Engine API","service":"compute.googleapis.com","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","containerInfo":"************","consumer":"projects/************"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}},"response":{"statusCode":403}},"exit":1,"message":"Request to https://compute.googleapis.com/compute/v1/projects/************ had HTTP Error: 403, Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","status":403}
[debug] [2025-07-28T03:11:36.632Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:36.632Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:36.632Z] >>> [apiv2][query] GET https://compute.googleapis.com/compute/v1/projects/************ [none]
[debug] [2025-07-28T03:11:36.895Z] <<< [apiv2][status] GET https://compute.googleapis.com/compute/v1/projects/************ 403
[debug] [2025-07-28T03:11:36.895Z] <<< [apiv2][body] GET https://compute.googleapis.com/compute/v1/projects/************ {"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"consumer":"projects/************","containerInfo":"************","service":"compute.googleapis.com","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","serviceTitle":"Compute Engine API"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}}
[debug] [2025-07-28T03:11:36.895Z] unable to look up default compute service account. Falling <NAME_EMAIL>. Error: {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"consumer":"projects/************","containerInfo":"************","service":"compute.googleapis.com","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","serviceTitle":"Compute Engine API"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}},"response":{"statusCode":403}},"exit":1,"message":"Request to https://compute.googleapis.com/compute/v1/projects/************ had HTTP Error: 403, Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","status":403}
[debug] [2025-07-28T03:11:36.895Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:36.895Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:36.895Z] >>> [apiv2][query] GET https://compute.googleapis.com/compute/v1/projects/************ [none]
[debug] [2025-07-28T03:11:37.161Z] <<< [apiv2][status] GET https://compute.googleapis.com/compute/v1/projects/************ 403
[debug] [2025-07-28T03:11:37.161Z] <<< [apiv2][body] GET https://compute.googleapis.com/compute/v1/projects/************ {"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"consumer":"projects/************","service":"compute.googleapis.com","serviceTitle":"Compute Engine API","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","containerInfo":"************"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}}
[debug] [2025-07-28T03:11:37.161Z] unable to look up default compute service account. Falling <NAME_EMAIL>. Error: {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"consumer":"projects/************","service":"compute.googleapis.com","serviceTitle":"Compute Engine API","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","containerInfo":"************"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}},"response":{"statusCode":403}},"exit":1,"message":"Request to https://compute.googleapis.com/compute/v1/projects/************ had HTTP Error: 403, Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","status":403}
[debug] [2025-07-28T03:11:37.164Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:37.164Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:37.164Z] >>> [apiv2][query] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/speakoneai-dev-9f995/versions [none]
[debug] [2025-07-28T03:11:37.164Z] >>> [apiv2][body] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/speakoneai-dev-9f995/versions {"status":"CREATED","labels":{"deployment-tool":"cli-firebase","firebase-web-framework":"next_ssr"}}
[debug] [2025-07-28T03:11:38.692Z] <<< [apiv2][status] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/speakoneai-dev-9f995/versions 200
[debug] [2025-07-28T03:11:38.692Z] <<< [apiv2][body] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/speakoneai-dev-9f995/versions {"name":"projects/************/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a","status":"CREATED","config":{},"labels":{"firebase-web-framework":"next_ssr","deployment-tool":"cli-firebase"}}
[debug] [2025-07-28T03:11:38.692Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:38.692Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:38.693Z] >>> [apiv2][query] GET https://firestore.googleapis.com/v1/projects/speakoneai-dev-9f995/databases/(default) [none]
[debug] [2025-07-28T03:11:39.024Z] <<< [apiv2][status] GET https://firestore.googleapis.com/v1/projects/speakoneai-dev-9f995/databases/(default) 200
[debug] [2025-07-28T03:11:39.024Z] <<< [apiv2][body] GET https://firestore.googleapis.com/v1/projects/speakoneai-dev-9f995/databases/(default) {"name":"projects/speakoneai-dev-9f995/databases/(default)","uid":"fa70c046-39ce-4064-8932-d42d1fb16711","createTime":"2025-07-27T05:34:26.394421Z","updateTime":"2025-07-27T05:34:26.394421Z","locationId":"asia-east2","type":"FIRESTORE_NATIVE","concurrencyMode":"PESSIMISTIC","versionRetentionPeriod":"3600s","earliestVersionTime":"2025-07-28T02:11:39.510529Z","appEngineIntegrationMode":"DISABLED","keyPrefix":"n","pointInTimeRecoveryEnablement":"POINT_IN_TIME_RECOVERY_DISABLED","deleteProtectionState":"DELETE_PROTECTION_DISABLED","databaseEdition":"STANDARD","freeTier":false,"etag":"IPHv27PK3o4DMKXD7buo3I4D"}
[debug] [2025-07-28T03:11:39.025Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:39.025Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:39.025Z] >>> [apiv2][query] GET https://firebaserules.googleapis.com/v1/projects/speakoneai-dev-9f995/releases pageSize=10&pageToken=
[debug] [2025-07-28T03:11:40.094Z] <<< [apiv2][status] GET https://firebaserules.googleapis.com/v1/projects/speakoneai-dev-9f995/releases 200
[debug] [2025-07-28T03:11:40.095Z] <<< [apiv2][body] GET https://firebaserules.googleapis.com/v1/projects/speakoneai-dev-9f995/releases {"releases":[{"name":"projects/speakoneai-dev-9f995/releases/cloud.firestore","rulesetName":"projects/speakoneai-dev-9f995/rulesets/be7e9c1d-3f09-45eb-9d1f-7bf3bc8eb8f0","createTime":"2025-07-27T05:40:28.322525Z","updateTime":"2025-07-27T13:25:48.028066Z"},{"name":"projects/speakoneai-dev-9f995/releases/cloud.firestore/fs-speakoneai-dev","rulesetName":"projects/speakoneai-dev-9f995/rulesets/15f6b667-f61f-4750-88d9-f4a23f6c3dfb","createTime":"2025-07-27T05:25:15.259891Z","updateTime":"2025-07-27T05:25:15.259891Z"}]}
[debug] [2025-07-28T03:11:40.106Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:40.106Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:40.106Z] >>> [apiv2][query] GET https://firebaserules.googleapis.com/v1/projects/speakoneai-dev-9f995/rulesets/be7e9c1d-3f09-45eb-9d1f-7bf3bc8eb8f0 [none]
[debug] [2025-07-28T03:11:41.102Z] <<< [apiv2][status] GET https://firebaserules.googleapis.com/v1/projects/speakoneai-dev-9f995/rulesets/be7e9c1d-3f09-45eb-9d1f-7bf3bc8eb8f0 200
[debug] [2025-07-28T03:11:41.102Z] <<< [apiv2][body] GET https://firebaserules.googleapis.com/v1/projects/speakoneai-dev-9f995/rulesets/be7e9c1d-3f09-45eb-9d1f-7bf3bc8eb8f0 [omitted]
[info] i  firestore: uploading rules firestore.rules... 
[debug] [2025-07-28T03:11:41.103Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:41.103Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:41.104Z] >>> [apiv2][query] POST https://firebaserules.googleapis.com/v1/projects/speakoneai-dev-9f995/rulesets [none]
[debug] [2025-07-28T03:11:41.104Z] >>> [apiv2][body] POST https://firebaserules.googleapis.com/v1/projects/speakoneai-dev-9f995/rulesets [omitted]
[debug] [2025-07-28T03:11:42.152Z] <<< [apiv2][status] POST https://firebaserules.googleapis.com/v1/projects/speakoneai-dev-9f995/rulesets 200
[debug] [2025-07-28T03:11:42.152Z] <<< [apiv2][body] POST https://firebaserules.googleapis.com/v1/projects/speakoneai-dev-9f995/rulesets {"name":"projects/speakoneai-dev-9f995/rulesets/9b1509aa-9368-4ca0-9ebd-1d3f6b8a043c","source":{"files":[{"content":"rules_version = '2';\r\nservice cloud.firestore {\r\n  match /databases/{database}/documents {\r\n    \r\n    // Helper functions\r\n    function isAuthenticated() {\r\n      return request.auth != null;\r\n    }\r\n    \r\n    function isOwner(userId) {\r\n      return isAuthenticated() && request.auth.uid == userId;\r\n    }\r\n    \r\n    function isValidUser() {\r\n      return isAuthenticated() &&\r\n             (request.auth.token.email_verified == true ||\r\n              request.auth.token.firebase.sign_in_provider == 'google.com');\r\n    }\r\n    \r\n    // Users collection\r\n    match /users/{userId} {\r\n      // Users can read and write their own data\r\n      allow read, write: if isOwner(userId) && isValidUser();\r\n\r\n      // Allow creation during registration (Firebase Auth will handle this)\r\n      allow create: if isAuthenticated() &&\r\n                   request.auth.uid == userId &&\r\n                   (request.auth.token.email_verified == true ||\r\n                    request.auth.token.firebase.sign_in_provider == 'google.com');\r\n    }\r\n\r\n    \r\n    // Purchases collection\r\n    match /purchases/{purchaseId} {\r\n      // Users can read their own purchases\r\n      allow read: if isAuthenticated() && \r\n                 resource.data.userId == request.auth.uid;\r\n      \r\n      // Only server (via Admin SDK) can create/update purchases\r\n      // This happens via Stripe webhooks and checkout API\r\n      allow create, update: if false; // Handled by server-side functions\r\n    }\r\n    \r\n    // Usage logs collection\r\n    match /usage_logs/{logId} {\r\n      // Users can read their own usage logs\r\n      allow read: if isAuthenticated() && \r\n                 resource.data.userId == request.auth.uid;\r\n      \r\n      // Users can create usage logs (from client apps)\r\n      allow create: if isAuthenticated() && \r\n                   request.auth.uid == resource.data.userId &&\r\n                   isValidUsageLog();\r\n      \r\n      // Only allow updates to end session\r\n      allow update: if isAuthenticated() && \r\n                   request.auth.uid == resource.data.userId &&\r\n                   isValidUsageUpdate();\r\n    }\r\n    \r\n    // Validation functions for usage logs\r\n    function isValidUsageLog() {\r\n      let data = request.resource.data;\r\n      return data.keys().hasAll(['userId', 'sessionStart', 'platform', 'createdAt']) &&\r\n             data.userId == request.auth.uid &&\r\n             data.platform in ['windows', 'macos'] &&\r\n             data.sessionStart is timestamp &&\r\n             data.createdAt is timestamp;\r\n    }\r\n    \r\n    function isValidUsageUpdate() {\r\n      let data = request.resource.data;\r\n      let existing = resource.data;\r\n\r\n      // Only allow updating sessionEnd and secondsUsed\r\n      return data.diff(existing).affectedKeys().hasOnly(['sessionEnd', 'secondsUsed']) &&\r\n             data.sessionEnd is timestamp &&\r\n             data.secondsUsed is number &&\r\n             data.secondsUsed >= 0;\r\n    }\r\n    \r\n    // Deny all other collections\r\n    match /{document=**} {\r\n      allow read, write: if false;\r\n    }\r\n  }\r\n}\r\n","name":"firestore.rules"}]},"createTime":"2025-07-28T03:11:42.512655Z","metadata":{"services":["cloud.firestore"]}}
[debug] [2025-07-28T03:11:42.152Z] [rules] created ruleset projects/speakoneai-dev-9f995/rulesets/9b1509aa-9368-4ca0-9ebd-1d3f6b8a043c
[info] i  firestore: deploying indexes... 
[debug] [2025-07-28T03:11:42.154Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:42.154Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:42.154Z] >>> [apiv2][query] GET https://firestore.googleapis.com/v1/projects/speakoneai-dev-9f995/databases/(default)/collectionGroups/-/indexes [none]
[debug] [2025-07-28T03:11:42.368Z] <<< [apiv2][status] GET https://firestore.googleapis.com/v1/projects/speakoneai-dev-9f995/databases/(default)/collectionGroups/-/indexes 200
[debug] [2025-07-28T03:11:42.368Z] <<< [apiv2][body] GET https://firestore.googleapis.com/v1/projects/speakoneai-dev-9f995/databases/(default)/collectionGroups/-/indexes {"indexes":[{"name":"projects/speakoneai-dev-9f995/databases/(default)/collectionGroups/usage_logs/indexes/CICAgJjF9oIJ","queryScope":"COLLECTION","fields":[{"fieldPath":"device_id","order":"ASCENDING"},{"fieldPath":"created_at","order":"DESCENDING"},{"fieldPath":"__name__","order":"DESCENDING"}],"state":"READY","density":"SPARSE_ALL"},{"name":"projects/speakoneai-dev-9f995/databases/(default)/collectionGroups/devices/indexes/CICAgJim14AK","queryScope":"COLLECTION","fields":[{"fieldPath":"user_id","order":"ASCENDING"},{"fieldPath":"is_active","order":"ASCENDING"},{"fieldPath":"__name__","order":"ASCENDING"}],"state":"READY","density":"SPARSE_ALL"},{"name":"projects/speakoneai-dev-9f995/databases/(default)/collectionGroups/users/indexes/CICAgOjXh4EK","queryScope":"COLLECTION","fields":[{"fieldPath":"register_region_timezone","order":"ASCENDING"},{"fieldPath":"last_usage_reset_date","order":"ASCENDING"},{"fieldPath":"__name__","order":"ASCENDING"}],"state":"READY","density":"SPARSE_ALL"},{"name":"projects/speakoneai-dev-9f995/databases/(default)/collectionGroups/usage_logs/indexes/CICAgJj7z4EK","queryScope":"COLLECTION","fields":[{"fieldPath":"user_id","order":"ASCENDING"},{"fieldPath":"created_at","order":"DESCENDING"},{"fieldPath":"__name__","order":"DESCENDING"}],"state":"READY","density":"SPARSE_ALL"},{"name":"projects/speakoneai-dev-9f995/databases/(default)/collectionGroups/devices/indexes/CICAgJjF9oIK","queryScope":"COLLECTION","fields":[{"fieldPath":"user_id","order":"ASCENDING"},{"fieldPath":"platform","order":"ASCENDING"},{"fieldPath":"__name__","order":"ASCENDING"}],"state":"READY","density":"SPARSE_ALL"},{"name":"projects/speakoneai-dev-9f995/databases/(default)/collectionGroups/subscriptions/indexes/CICAgJiUpoMK","queryScope":"COLLECTION","fields":[{"fieldPath":"status","order":"ASCENDING"},{"fieldPath":"plan","order":"ASCENDING"},{"fieldPath":"__name__","order":"ASCENDING"}],"state":"READY","density":"SPARSE_ALL"},{"name":"projects/speakoneai-dev-9f995/databases/(default)/collectionGroups/usage_logs/indexes/CICAgNi47oMK","queryScope":"COLLECTION","fields":[{"fieldPath":"user_id","order":"ASCENDING"},{"fieldPath":"daily_reset_date","order":"ASCENDING"},{"fieldPath":"__name__","order":"ASCENDING"}],"state":"READY","density":"SPARSE_ALL"}]}
[debug] [2025-07-28T03:11:42.369Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:42.369Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:11:42.369Z] >>> [apiv2][query] GET https://firestore.googleapis.com/v1/projects/speakoneai-dev-9f995/databases/(default)/collectionGroups/-/fields?filter=indexConfig.usesAncestorConfig=false OR ttlConfig:* [none]
[debug] [2025-07-28T03:11:42.606Z] <<< [apiv2][status] GET https://firestore.googleapis.com/v1/projects/speakoneai-dev-9f995/databases/(default)/collectionGroups/-/fields?filter=indexConfig.usesAncestorConfig=false OR ttlConfig:* 200
[debug] [2025-07-28T03:11:42.606Z] <<< [apiv2][body] GET https://firestore.googleapis.com/v1/projects/speakoneai-dev-9f995/databases/(default)/collectionGroups/-/fields?filter=indexConfig.usesAncestorConfig=false OR ttlConfig:* {"fields":[{"name":"projects/speakoneai-dev-9f995/databases/(default)/collectionGroups/__default__/fields/*","indexConfig":{"indexes":[{"queryScope":"COLLECTION","fields":[{"fieldPath":"*","order":"ASCENDING"}],"state":"READY"},{"queryScope":"COLLECTION","fields":[{"fieldPath":"*","order":"DESCENDING"}],"state":"READY"},{"queryScope":"COLLECTION","fields":[{"fieldPath":"*","arrayConfig":"CONTAINS"}],"state":"READY"}]}}]}
[info] i  firestore: The following indexes are defined in your project but are not present in your firestore indexes file:
	(usage_logs) -- (device_id,ASCENDING) (created_at,DESCENDING) 
	(devices) -- (user_id,ASCENDING) (is_active,ASCENDING) 
	(users) -- (register_region_timezone,ASCENDING) (last_usage_reset_date,ASCENDING) 
	(usage_logs) -- (user_id,ASCENDING) (created_at,DESCENDING) 
	(devices) -- (user_id,ASCENDING) (platform,ASCENDING) 
	(subscriptions) -- (status,ASCENDING) (plan,ASCENDING) 
	(usage_logs) -- (user_id,ASCENDING) (daily_reset_date,ASCENDING)  
[info] +  firestore: deployed indexes in firestore.indexes.json successfully for (default) database 
[debug] [2025-07-28T03:12:00.546Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:00.546Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:00.546Z] >>> [apiv2][query] GET https://cloudbilling.googleapis.com/v1/projects/speakoneai-dev-9f995/billingInfo [none]
[debug] [2025-07-28T03:12:01.120Z] <<< [apiv2][status] GET https://cloudbilling.googleapis.com/v1/projects/speakoneai-dev-9f995/billingInfo 200
[debug] [2025-07-28T03:12:01.121Z] <<< [apiv2][body] GET https://cloudbilling.googleapis.com/v1/projects/speakoneai-dev-9f995/billingInfo {"name":"projects/speakoneai-dev-9f995/billingInfo","projectId":"speakoneai-dev-9f995","billingAccountName":"billingAccounts/01684D-4E7735-F9857A","billingEnabled":true}
[debug] [2025-07-28T03:12:01.121Z] [functions] found 3 new HTTP functions, testing setIamPolicy permission...
[debug] [2025-07-28T03:12:01.121Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:01.122Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:01.122Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995:testIamPermissions [none]
[debug] [2025-07-28T03:12:01.122Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995:testIamPermissions x-goog-quota-user=projects/speakoneai-dev-9f995
[debug] [2025-07-28T03:12:01.122Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995:testIamPermissions {"permissions":["cloudfunctions.functions.setIamPolicy"]}
[debug] [2025-07-28T03:12:02.012Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995:testIamPermissions 200
[debug] [2025-07-28T03:12:02.013Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/speakoneai-dev-9f995:testIamPermissions {"permissions":["cloudfunctions.functions.setIamPolicy"]}
[debug] [2025-07-28T03:12:02.013Z] [functions] found setIamPolicy permission, proceeding with deploy
[debug] [2025-07-28T03:12:02.013Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:02.013Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:02.013Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:02.013Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:02.013Z] >>> [apiv2][query] POST https://cloudfunctions.googleapis.com/v2/projects/speakoneai-dev-9f995/locations/us-central1/functions:generateUploadUrl [none]
[debug] [2025-07-28T03:12:02.015Z] >>> [apiv2][query] POST https://cloudfunctions.googleapis.com/v2/projects/speakoneai-dev-9f995/locations/asia-east1/functions:generateUploadUrl [none]
[debug] [2025-07-28T03:12:02.246Z] <<< [apiv2][status] POST https://cloudfunctions.googleapis.com/v2/projects/speakoneai-dev-9f995/locations/asia-east1/functions:generateUploadUrl 200
[debug] [2025-07-28T03:12:02.246Z] <<< [apiv2][body] POST https://cloudfunctions.googleapis.com/v2/projects/speakoneai-dev-9f995/locations/asia-east1/functions:generateUploadUrl {"uploadUrl":"https://storage.googleapis.com/gcf-v2-uploads-************.asia-east1.cloudfunctions.appspot.com/be0d28a9-d176-4769-ab5a-38ddb47a0441.zip?GoogleAccessId=<EMAIL>&Expires=**********&Signature=x%2FMTWqnKQ2Gucg%2FJq6irzFC11IEFKuzKGWfWCwd140sV9OZk57y7PbDAEQkBVArmkLwmjDUeC6ouikWw%2Bz%2F8FJvNZN1ZnPUn7duvyiCpeOUyYkRlvLcWr1ZHP7q4UzgIF06uX%2B2ILInSyHiwA3oF3BmLLHxkP41bsB5kG3jiZOwow37GccW3N%2FoiD6NSFXZcDZbxxcpRPvXZENrPEXBMAk6XbLwBix6HHPE6yIYzvC7Q2N1xOLEVH0a43z7QhNlbsz002v1orQb7k%2FmlhzutBUIC22P82jUnLz6WdmvyQ3OsNgGBvT2STN4RxTklc0xdmmkPFQkPHPRKzZWL1eDlHQ%3D%3D","storageSource":{"bucket":"gcf-v2-uploads-************.asia-east1.cloudfunctions.appspot.com","object":"be0d28a9-d176-4769-ab5a-38ddb47a0441.zip"}}
[debug] [2025-07-28T03:12:02.247Z] >>> [apiv2][query] PUT https://storage.googleapis.com/gcf-v2-uploads-************.asia-east1.cloudfunctions.appspot.com/be0d28a9-d176-4769-ab5a-38ddb47a0441.zip GoogleAccessId=service-************%40gcf-admin-robot.iam.gserviceaccount.com&Expires=**********&Signature=x%2FMTWqnKQ2Gucg%2FJq6irzFC11IEFKuzKGWfWCwd140sV9OZk57y7PbDAEQkBVArmkLwmjDUeC6ouikWw%2Bz%2F8FJvNZN1ZnPUn7duvyiCpeOUyYkRlvLcWr1ZHP7q4UzgIF06uX%2B2ILInSyHiwA3oF3BmLLHxkP41bsB5kG3jiZOwow37GccW3N%2FoiD6NSFXZcDZbxxcpRPvXZENrPEXBMAk6XbLwBix6HHPE6yIYzvC7Q2N1xOLEVH0a43z7QhNlbsz002v1orQb7k%2FmlhzutBUIC22P82jUnLz6WdmvyQ3OsNgGBvT2STN4RxTklc0xdmmkPFQkPHPRKzZWL1eDlHQ%3D%3D
[debug] [2025-07-28T03:12:02.247Z] >>> [apiv2][body] PUT https://storage.googleapis.com/gcf-v2-uploads-************.asia-east1.cloudfunctions.appspot.com/be0d28a9-d176-4769-ab5a-38ddb47a0441.zip [stream]
[debug] [2025-07-28T03:12:03.526Z] <<< [apiv2][status] PUT https://storage.googleapis.com/gcf-v2-uploads-************.asia-east1.cloudfunctions.appspot.com/be0d28a9-d176-4769-ab5a-38ddb47a0441.zip 200
[debug] [2025-07-28T03:12:03.526Z] <<< [apiv2][body] PUT https://storage.googleapis.com/gcf-v2-uploads-************.asia-east1.cloudfunctions.appspot.com/be0d28a9-d176-4769-ab5a-38ddb47a0441.zip [omitted]
[info] +  functions: .firebase\speakoneai-dev-9f995\functions folder uploaded successfully 
[debug] [2025-07-28T03:12:04.277Z] <<< [apiv2][status] POST https://cloudfunctions.googleapis.com/v2/projects/speakoneai-dev-9f995/locations/us-central1/functions:generateUploadUrl 200
[debug] [2025-07-28T03:12:04.277Z] <<< [apiv2][body] POST https://cloudfunctions.googleapis.com/v2/projects/speakoneai-dev-9f995/locations/us-central1/functions:generateUploadUrl {"uploadUrl":"https://storage.googleapis.com/gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com/97fdd53e-c825-4748-adfe-b0b872402ecc.zip?GoogleAccessId=<EMAIL>&Expires=**********&Signature=yf4vNLOsH2EXCVlyV9FozqYTxMfZG9pk0uUrbC%2FqnvAKUexRihnPwl2qSsr%2FuUzmNiL9YtVISLBcR%2Fyvzcu9HBVvdn8arVuiN61bpG3spiEDTdZkSuwrsXFQPNJD1aBN7%2FJyEKAlIjck5yxtmBVsT1XZd1TA1svJxfqd5mGFCc9XFgFF%2FyBOEmJ3sP%2F7Niz4RsUm7RWJABxu1cQo4%2F4gXDXBh66SqRGFWF4alXe2hEsNT3gf2z2PB5tMbD6ACWtdFBnn2XixJbhdXNUThSEAAyyrk8xrxWf64urLdaB8dsR5oJOm2sRwSKREd1pvetAXS1eLd%2Btt6izRN6qrvUxW4A%3D%3D","storageSource":{"bucket":"gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com","object":"97fdd53e-c825-4748-adfe-b0b872402ecc.zip"}}
[debug] [2025-07-28T03:12:04.277Z] >>> [apiv2][query] PUT https://storage.googleapis.com/gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com/97fdd53e-c825-4748-adfe-b0b872402ecc.zip GoogleAccessId=service-************%40gcf-admin-robot.iam.gserviceaccount.com&Expires=**********&Signature=yf4vNLOsH2EXCVlyV9FozqYTxMfZG9pk0uUrbC%2FqnvAKUexRihnPwl2qSsr%2FuUzmNiL9YtVISLBcR%2Fyvzcu9HBVvdn8arVuiN61bpG3spiEDTdZkSuwrsXFQPNJD1aBN7%2FJyEKAlIjck5yxtmBVsT1XZd1TA1svJxfqd5mGFCc9XFgFF%2FyBOEmJ3sP%2F7Niz4RsUm7RWJABxu1cQo4%2F4gXDXBh66SqRGFWF4alXe2hEsNT3gf2z2PB5tMbD6ACWtdFBnn2XixJbhdXNUThSEAAyyrk8xrxWf64urLdaB8dsR5oJOm2sRwSKREd1pvetAXS1eLd%2Btt6izRN6qrvUxW4A%3D%3D
[debug] [2025-07-28T03:12:04.277Z] >>> [apiv2][body] PUT https://storage.googleapis.com/gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com/97fdd53e-c825-4748-adfe-b0b872402ecc.zip [stream]
[debug] [2025-07-28T03:12:04.795Z] <<< [apiv2][status] PUT https://storage.googleapis.com/gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com/97fdd53e-c825-4748-adfe-b0b872402ecc.zip 200
[debug] [2025-07-28T03:12:04.795Z] <<< [apiv2][body] PUT https://storage.googleapis.com/gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com/97fdd53e-c825-4748-adfe-b0b872402ecc.zip [omitted]
[info] +  functions: functions folder uploaded successfully 
[info] i  hosting[speakoneai-dev-9f995]: beginning deploy... 
[info] i  hosting[speakoneai-dev-9f995]: found 44 files in .firebase\speakoneai-dev-9f995\hosting 
[debug] [2025-07-28T03:12:04.813Z] [hosting] uploading with 200 concurrency
[debug] [2025-07-28T03:12:04.814Z] [hosting] hash cache [LmZpcmViYXNlXHNwZWFrb25lYWktZGV2LTlmOTk1XGhvc3Rpbmc] not populated
[debug] [2025-07-28T03:12:04.872Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:04.872Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:04.874Z] [hosting] hash cache [LmZpcmViYXNlXHNwZWFrb25lYWktZGV2LTlmOTk1XGhvc3Rpbmc] stored for 44 files
[debug] [2025-07-28T03:12:04.874Z] [hosting][hash queue][FINAL] {"max":56,"min":17,"avg":21.72727272727273,"active":0,"complete":44,"success":44,"errored":0,"retried":0,"total":44,"elapsed":59}
[debug] [2025-07-28T03:12:04.874Z] >>> [apiv2][query] POST https://firebasehosting.googleapis.com/v1beta1/projects/************/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a:populateFiles [none]
[debug] [2025-07-28T03:12:04.874Z] >>> [apiv2][body] POST https://firebasehosting.googleapis.com/v1beta1/projects/************/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a:populateFiles {"files":{"/favicon-32x32.png":"dd7fa71f011c54d0d49dd54c627836d5849d8cb5d70dfb4338e3fc0cdff0cbeb","/favicon.ico":"ea6f82423f06ce8137da97f9d0abcd531bd812b9cc91f7ee8eb1d7387bb84ab3","/favicon-16x16.png":"748f1a863f072df2df18273ddcc6aadb6870bc7f1d120e5752aaadbfde581555","/pricing.html":"3ac4b584a965ee22e946fa1df329513cca662e9943bf55cac9e70b9d98fe144d","/dashboard.html":"99da8626a96998ada75bdb6578f5c43552b5af3b584e184f1c221c499c0daf00","/404.html":"92c47ba54816eb7ce01ea126549fdfcb8fa5452478552661958bb137c6cdf079","/_next/static/OFQtJ3jvcQ2aCRsYPSACw/_buildManifest.js":"bf58e3dfce8d4fa3b08918b490f53dab1f2afc4de91ea1a1dc17abe488bb2b39","/index.html":"f08e630d78cd031feb1d3b966e18610dc113364eb1ae635433c7741c148fafe4","/_next/static/media/97e0cb1ae144a2a9-s.woff2":"7f297ea688c4600e59ca6534a3f21f53bfa3f286cc0d19a35543315781951381","/_next/static/media/df0a9ae256c0569c-s.woff2":"5ef466d8ca6f4a51fa3751faea59ad2f63b15dbf9782ae4246bcadacc57f6b8f","/_next/static/OFQtJ3jvcQ2aCRsYPSACw/_ssgManifest.js":"dc28a4dc92fe352ed5d2201bd3972ce47691bc8e89e0400a68d1541d0567c6d5","/_next/static/chunks/main-app-c89cbff2c0a5e0b4.js":"5de8299382a8204fb6d74f4f24d0f4fd1b69d25785f68be54d61f338513b0f10","/_next/static/chunks/webpack-2b150d4b26de24cc.js":"b33ffed4d95bef3e472c6045b54042b4c7a168098ddc92755cca7b404241c3f9","/_next/static/css/0a7513e721bca3f6.css":"eb84f6b47de0b8f806623f3825462c9a1a67fe7c069c77c0b5130611eda45ee3","/_next/static/chunks/865.56b2adf1c72878fa.js":"ba784143151802dfe7c9b9f87cb020366dd85efea3c991166aee8950320960bf","/_next/static/chunks/573-8b417b463067fd1f.js":"42308c61be399a20fcfa0a7f987b2dbd21915b32d9244b8451493e87caeebaea","/_next/static/chunks/893.fb6384bf9438f542.js":"45e6a841ee00b2582b931315c85ba1d9782e57cb86ae86ae0de3cdae215ba6d8","/_next/static/chunks/473-bb0a8309a60a161f.js":"d7fecb9fd31a17180f5926be6a4c60f2dcfbd33b675a8df380cbb220bd398107","/_next/static/chunks/472.a3826d29d6854395.js":"305e361c64ccf8c2f956618d58efccb54e7dfc79013eca70fe4e27abf0e2724e","/_next/static/chunks/341.df3329d77a5faa19.js":"9563f2302179d3abf33dd49b6eab6595a1cdeb7d581ca034be150957a06529e3","/_next/static/chunks/pages/_app-92f2aae776f86b9c.js":"f7ae3774eb08a03f3e0162b5074ae3e767bd46332f84cb20bca3ab1aa1f72eaf","/_next/static/chunks/app/layout-f04b8b863254b907.js":"78c51412992619e4d22fd3ce9f114d88191e5e0379c74db222c16a5baead6f75","/_next/static/chunks/app/page-e4ccbfaedc5d5c5e.js":"7b9629c2b333178bb07b150c1ec67af9b7e4f34e7d9f4a445bd00baac2ce8dba","/_next/static/chunks/pages/_error-71d2b6a7b832d02a.js":"36785219d9c2deae036e57c6807546fe7d1e5152565ab15ac68edd304c8e02dc","/_next/static/chunks/app/api/webhooks/stripe/route-f79db4a3576b157b.js":"13d0541d4c8722ed769f24ac00c8aaa101a87875623e51be179eaa5aeedb0511","/_next/static/chunks/app/api/checkout/route-53a1cb806fdfaed5.js":"13d0541d4c8722ed769f24ac00c8aaa101a87875623e51be179eaa5aeedb0511","/_next/static/chunks/app/_not-found/page-66b31666f90dea96.js":"57699909ca93875b9d5a282c5aa1b0771ed6bd28a90c5cdbe75525f721f28b87","/_next/static/chunks/app/pricing/page-94d6093ea3a4af46.js":"4687f404a5aa344d71aec22103313c7d4500af225121321d613fff8821f3104c","/_next/static/chunks/app/dashboard/page-11ee5e5d6741bcbc.js":"bbf5c4a855371072bf3fec6e5074044f1b505b8856df343966c11e851eef3ab6","/_next/static/media/581909926a08bbc8-s.woff2":"0e60a16188a2d115835bb0497cc663807ef4c951ea3033aaaa98e4e9605510e1","/_next/static/media/26a46d62cd723877-s.woff2":"ae5c06e2307aad7bf8fb02467ddd2ea6d7f40ea27915c5d9667f7632db6a0dbc","/_next/static/media/55c55f0601d81cf3-s.woff2":"f919b2595b36cbe21633695ac08cbc808d615f1e5e964227b64c6d398caa0332","/_next/static/media/e4af272ccee01ff0-s.p.woff2":"86835ac53a0e1de04820388d01b501389d5e9311509282b5e3bfbfc752754083","/apple-touch-icon.png":"8b181632bc7307d4e5da89c6f45a582dd56f78c190b182327e938d5cbebc0da7","/_next/static/chunks/main-829d43144a777921.js":"dac2436658d875db7a6d536198b657f5d79e97b575eb544cf45755323faea9b5","/_next/static/chunks/ae6eea6a-4b838974dc2ed083.js":"54a1304996fa75ed51569bae3a41842579ca44ec9073e12cf1c21b2d090c045b","/_next/static/chunks/polyfills-42372ed130431b0a.js":"18e28d3214eda45048d80d3925ea7627b809e69ad2e95f7f98459e9146a61c3d","/_next/static/chunks/684-5515cb541f008308.js":"09d39de1e9e65511b5c3defdaa3cf2190f49be6511a41af16afd96d520bd300e","/_next/static/media/8e9860b6e62d6359-s.woff2":"cd2992a875e4939d47289a3cc44c8b8a0e63817f4cc9fbd8b3bdf93b5f66fbb3","/_next/static/chunks/4bd1b696-7d52f47172210184.js":"99db1cc472f86b1e139c5d34542f09147d0df9ebd3468166b316b80fff8bbd74","/_next/static/chunks/243-f40837ebe835e746.js":"da625e0e171483552010289b47f694a6141bcb667402cec6f5b910c3275a45ed","/_next/static/chunks/framework-f593a28cde54158e.js":"c0e31b38c07fb2cac30269c70d55077e4f6a99e360e0c256f9b6f2d74adf24e4","/_next/static/chunks/bc9e92e6-3552291efbbd7960.js":"608b98a9a230c8b9088009b744c8e4a9cc5509623da5149faf691733acf5590b","/logo.png":"03f878fc187d883a715b00718f7b6186438b4c61074e17ce0cbc623eb5a59718"}}
[debug] [2025-07-28T03:12:05.698Z] <<< [apiv2][status] POST https://firebasehosting.googleapis.com/v1beta1/projects/************/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a:populateFiles 200
[debug] [2025-07-28T03:12:05.698Z] <<< [apiv2][body] POST https://firebasehosting.googleapis.com/v1beta1/projects/************/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a:populateFiles {"uploadRequiredHashes":["18e28d3214eda45048d80d3925ea7627b809e69ad2e95f7f98459e9146a61c3d","dd7fa71f011c54d0d49dd54c627836d5849d8cb5d70dfb4338e3fc0cdff0cbeb","608b98a9a230c8b9088009b744c8e4a9cc5509623da5149faf691733acf5590b","bf58e3dfce8d4fa3b08918b490f53dab1f2afc4de91ea1a1dc17abe488bb2b39","7b9629c2b333178bb07b150c1ec67af9b7e4f34e7d9f4a445bd00baac2ce8dba","f919b2595b36cbe21633695ac08cbc808d615f1e5e964227b64c6d398caa0332","09d39de1e9e65511b5c3defdaa3cf2190f49be6511a41af16afd96d520bd300e","92c47ba54816eb7ce01ea126549fdfcb8fa5452478552661958bb137c6cdf079","dc28a4dc92fe352ed5d2201bd3972ce47691bc8e89e0400a68d1541d0567c6d5","4687f404a5aa344d71aec22103313c7d4500af225121321d613fff8821f3104c","36785219d9c2deae036e57c6807546fe7d1e5152565ab15ac68edd304c8e02dc","7f297ea688c4600e59ca6534a3f21f53bfa3f286cc0d19a35543315781951381","45e6a841ee00b2582b931315c85ba1d9782e57cb86ae86ae0de3cdae215ba6d8","78c51412992619e4d22fd3ce9f114d88191e5e0379c74db222c16a5baead6f75","305e361c64ccf8c2f956618d58efccb54e7dfc79013eca70fe4e27abf0e2724e","8b181632bc7307d4e5da89c6f45a582dd56f78c190b182327e938d5cbebc0da7","0e60a16188a2d115835bb0497cc663807ef4c951ea3033aaaa98e4e9605510e1","f08e630d78cd031feb1d3b966e18610dc113364eb1ae635433c7741c148fafe4","5de8299382a8204fb6d74f4f24d0f4fd1b69d25785f68be54d61f338513b0f10","13d0541d4c8722ed769f24ac00c8aaa101a87875623e51be179eaa5aeedb0511","f7ae3774eb08a03f3e0162b5074ae3e767bd46332f84cb20bca3ab1aa1f72eaf","3ac4b584a965ee22e946fa1df329513cca662e9943bf55cac9e70b9d98fe144d","5ef466d8ca6f4a51fa3751faea59ad2f63b15dbf9782ae4246bcadacc57f6b8f","99da8626a96998ada75bdb6578f5c43552b5af3b584e184f1c221c499c0daf00","b33ffed4d95bef3e472c6045b54042b4c7a168098ddc92755cca7b404241c3f9","da625e0e171483552010289b47f694a6141bcb667402cec6f5b910c3275a45ed","57699909ca93875b9d5a282c5aa1b0771ed6bd28a90c5cdbe75525f721f28b87","ba784143151802dfe7c9b9f87cb020366dd85efea3c991166aee8950320960bf","99db1cc472f86b1e139c5d34542f09147d0df9ebd3468166b316b80fff8bbd74","03f878fc187d883a715b00718f7b6186438b4c61074e17ce0cbc623eb5a59718","bbf5c4a855371072bf3fec6e5074044f1b505b8856df343966c11e851eef3ab6","9563f2302179d3abf33dd49b6eab6595a1cdeb7d581ca034be150957a06529e3","86835ac53a0e1de04820388d01b501389d5e9311509282b5e3bfbfc752754083","dac2436658d875db7a6d536198b657f5d79e97b575eb544cf45755323faea9b5","ea6f82423f06ce8137da97f9d0abcd531bd812b9cc91f7ee8eb1d7387bb84ab3","d7fecb9fd31a17180f5926be6a4c60f2dcfbd33b675a8df380cbb220bd398107","748f1a863f072df2df18273ddcc6aadb6870bc7f1d120e5752aaadbfde581555","cd2992a875e4939d47289a3cc44c8b8a0e63817f4cc9fbd8b3bdf93b5f66fbb3","c0e31b38c07fb2cac30269c70d55077e4f6a99e360e0c256f9b6f2d74adf24e4","eb84f6b47de0b8f806623f3825462c9a1a67fe7c069c77c0b5130611eda45ee3","42308c61be399a20fcfa0a7f987b2dbd21915b32d9244b8451493e87caeebaea","54a1304996fa75ed51569bae3a41842579ca44ec9073e12cf1c21b2d090c045b","ae5c06e2307aad7bf8fb02467ddd2ea6d7f40ea27915c5d9667f7632db6a0dbc"],"uploadUrl":"https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files"}
[debug] [2025-07-28T03:12:05.699Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.700Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.700Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.700Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.700Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.700Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.700Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.700Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.700Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.700Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.700Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.700Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.700Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.700Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.701Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.702Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.703Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.703Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.703Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.703Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.703Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.703Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.703Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.703Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.703Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.703Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.703Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.703Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.703Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.703Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-28T03:12:05.703Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/18e28d3214eda45048d80d3925ea7627b809e69ad2e95f7f98459e9146a61c3d [none]
[debug] [2025-07-28T03:12:05.703Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/18e28d3214eda45048d80d3925ea7627b809e69ad2e95f7f98459e9146a61c3d [stream]
[debug] [2025-07-28T03:12:05.704Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/dd7fa71f011c54d0d49dd54c627836d5849d8cb5d70dfb4338e3fc0cdff0cbeb [none]
[debug] [2025-07-28T03:12:05.704Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/dd7fa71f011c54d0d49dd54c627836d5849d8cb5d70dfb4338e3fc0cdff0cbeb [stream]
[debug] [2025-07-28T03:12:05.705Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/608b98a9a230c8b9088009b744c8e4a9cc5509623da5149faf691733acf5590b [none]
[debug] [2025-07-28T03:12:05.705Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/608b98a9a230c8b9088009b744c8e4a9cc5509623da5149faf691733acf5590b [stream]
[debug] [2025-07-28T03:12:05.705Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/bf58e3dfce8d4fa3b08918b490f53dab1f2afc4de91ea1a1dc17abe488bb2b39 [none]
[debug] [2025-07-28T03:12:05.705Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/bf58e3dfce8d4fa3b08918b490f53dab1f2afc4de91ea1a1dc17abe488bb2b39 [stream]
[debug] [2025-07-28T03:12:05.706Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/7b9629c2b333178bb07b150c1ec67af9b7e4f34e7d9f4a445bd00baac2ce8dba [none]
[debug] [2025-07-28T03:12:05.706Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/7b9629c2b333178bb07b150c1ec67af9b7e4f34e7d9f4a445bd00baac2ce8dba [stream]
[debug] [2025-07-28T03:12:05.707Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/f919b2595b36cbe21633695ac08cbc808d615f1e5e964227b64c6d398caa0332 [none]
[debug] [2025-07-28T03:12:05.707Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/f919b2595b36cbe21633695ac08cbc808d615f1e5e964227b64c6d398caa0332 [stream]
[debug] [2025-07-28T03:12:05.708Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/09d39de1e9e65511b5c3defdaa3cf2190f49be6511a41af16afd96d520bd300e [none]
[debug] [2025-07-28T03:12:05.708Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/09d39de1e9e65511b5c3defdaa3cf2190f49be6511a41af16afd96d520bd300e [stream]
[debug] [2025-07-28T03:12:05.708Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/92c47ba54816eb7ce01ea126549fdfcb8fa5452478552661958bb137c6cdf079 [none]
[debug] [2025-07-28T03:12:05.708Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/92c47ba54816eb7ce01ea126549fdfcb8fa5452478552661958bb137c6cdf079 [stream]
[debug] [2025-07-28T03:12:05.709Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/dc28a4dc92fe352ed5d2201bd3972ce47691bc8e89e0400a68d1541d0567c6d5 [none]
[debug] [2025-07-28T03:12:05.709Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/dc28a4dc92fe352ed5d2201bd3972ce47691bc8e89e0400a68d1541d0567c6d5 [stream]
[debug] [2025-07-28T03:12:05.709Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/4687f404a5aa344d71aec22103313c7d4500af225121321d613fff8821f3104c [none]
[debug] [2025-07-28T03:12:05.709Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/4687f404a5aa344d71aec22103313c7d4500af225121321d613fff8821f3104c [stream]
[debug] [2025-07-28T03:12:05.710Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/36785219d9c2deae036e57c6807546fe7d1e5152565ab15ac68edd304c8e02dc [none]
[debug] [2025-07-28T03:12:05.710Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/36785219d9c2deae036e57c6807546fe7d1e5152565ab15ac68edd304c8e02dc [stream]
[debug] [2025-07-28T03:12:05.710Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/7f297ea688c4600e59ca6534a3f21f53bfa3f286cc0d19a35543315781951381 [none]
[debug] [2025-07-28T03:12:05.710Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/7f297ea688c4600e59ca6534a3f21f53bfa3f286cc0d19a35543315781951381 [stream]
[debug] [2025-07-28T03:12:05.711Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/45e6a841ee00b2582b931315c85ba1d9782e57cb86ae86ae0de3cdae215ba6d8 [none]
[debug] [2025-07-28T03:12:05.711Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/45e6a841ee00b2582b931315c85ba1d9782e57cb86ae86ae0de3cdae215ba6d8 [stream]
[debug] [2025-07-28T03:12:05.711Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/78c51412992619e4d22fd3ce9f114d88191e5e0379c74db222c16a5baead6f75 [none]
[debug] [2025-07-28T03:12:05.711Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/78c51412992619e4d22fd3ce9f114d88191e5e0379c74db222c16a5baead6f75 [stream]
[debug] [2025-07-28T03:12:05.712Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/305e361c64ccf8c2f956618d58efccb54e7dfc79013eca70fe4e27abf0e2724e [none]
[debug] [2025-07-28T03:12:05.712Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/305e361c64ccf8c2f956618d58efccb54e7dfc79013eca70fe4e27abf0e2724e [stream]
[debug] [2025-07-28T03:12:05.712Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/8b181632bc7307d4e5da89c6f45a582dd56f78c190b182327e938d5cbebc0da7 [none]
[debug] [2025-07-28T03:12:05.712Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/8b181632bc7307d4e5da89c6f45a582dd56f78c190b182327e938d5cbebc0da7 [stream]
[debug] [2025-07-28T03:12:05.713Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/0e60a16188a2d115835bb0497cc663807ef4c951ea3033aaaa98e4e9605510e1 [none]
[debug] [2025-07-28T03:12:05.713Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/0e60a16188a2d115835bb0497cc663807ef4c951ea3033aaaa98e4e9605510e1 [stream]
[debug] [2025-07-28T03:12:05.713Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/f08e630d78cd031feb1d3b966e18610dc113364eb1ae635433c7741c148fafe4 [none]
[debug] [2025-07-28T03:12:05.713Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/f08e630d78cd031feb1d3b966e18610dc113364eb1ae635433c7741c148fafe4 [stream]
[debug] [2025-07-28T03:12:05.714Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/5de8299382a8204fb6d74f4f24d0f4fd1b69d25785f68be54d61f338513b0f10 [none]
[debug] [2025-07-28T03:12:05.714Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/5de8299382a8204fb6d74f4f24d0f4fd1b69d25785f68be54d61f338513b0f10 [stream]
[debug] [2025-07-28T03:12:05.714Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/13d0541d4c8722ed769f24ac00c8aaa101a87875623e51be179eaa5aeedb0511 [none]
[debug] [2025-07-28T03:12:05.714Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/13d0541d4c8722ed769f24ac00c8aaa101a87875623e51be179eaa5aeedb0511 [stream]
[debug] [2025-07-28T03:12:05.715Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/f7ae3774eb08a03f3e0162b5074ae3e767bd46332f84cb20bca3ab1aa1f72eaf [none]
[debug] [2025-07-28T03:12:05.715Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/f7ae3774eb08a03f3e0162b5074ae3e767bd46332f84cb20bca3ab1aa1f72eaf [stream]
[debug] [2025-07-28T03:12:05.716Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/3ac4b584a965ee22e946fa1df329513cca662e9943bf55cac9e70b9d98fe144d [none]
[debug] [2025-07-28T03:12:05.716Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/3ac4b584a965ee22e946fa1df329513cca662e9943bf55cac9e70b9d98fe144d [stream]
[debug] [2025-07-28T03:12:05.716Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/5ef466d8ca6f4a51fa3751faea59ad2f63b15dbf9782ae4246bcadacc57f6b8f [none]
[debug] [2025-07-28T03:12:05.716Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/5ef466d8ca6f4a51fa3751faea59ad2f63b15dbf9782ae4246bcadacc57f6b8f [stream]
[debug] [2025-07-28T03:12:05.717Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/99da8626a96998ada75bdb6578f5c43552b5af3b584e184f1c221c499c0daf00 [none]
[debug] [2025-07-28T03:12:05.717Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/99da8626a96998ada75bdb6578f5c43552b5af3b584e184f1c221c499c0daf00 [stream]
[debug] [2025-07-28T03:12:05.717Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/b33ffed4d95bef3e472c6045b54042b4c7a168098ddc92755cca7b404241c3f9 [none]
[debug] [2025-07-28T03:12:05.717Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/b33ffed4d95bef3e472c6045b54042b4c7a168098ddc92755cca7b404241c3f9 [stream]
[debug] [2025-07-28T03:12:05.718Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/da625e0e171483552010289b47f694a6141bcb667402cec6f5b910c3275a45ed [none]
[debug] [2025-07-28T03:12:05.718Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/da625e0e171483552010289b47f694a6141bcb667402cec6f5b910c3275a45ed [stream]
[debug] [2025-07-28T03:12:05.718Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/57699909ca93875b9d5a282c5aa1b0771ed6bd28a90c5cdbe75525f721f28b87 [none]
[debug] [2025-07-28T03:12:05.718Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/57699909ca93875b9d5a282c5aa1b0771ed6bd28a90c5cdbe75525f721f28b87 [stream]
[debug] [2025-07-28T03:12:05.719Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/ba784143151802dfe7c9b9f87cb020366dd85efea3c991166aee8950320960bf [none]
[debug] [2025-07-28T03:12:05.719Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/ba784143151802dfe7c9b9f87cb020366dd85efea3c991166aee8950320960bf [stream]
[debug] [2025-07-28T03:12:05.720Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/99db1cc472f86b1e139c5d34542f09147d0df9ebd3468166b316b80fff8bbd74 [none]
[debug] [2025-07-28T03:12:05.720Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/99db1cc472f86b1e139c5d34542f09147d0df9ebd3468166b316b80fff8bbd74 [stream]
[debug] [2025-07-28T03:12:05.721Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/03f878fc187d883a715b00718f7b6186438b4c61074e17ce0cbc623eb5a59718 [none]
[debug] [2025-07-28T03:12:05.721Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/03f878fc187d883a715b00718f7b6186438b4c61074e17ce0cbc623eb5a59718 [stream]
[debug] [2025-07-28T03:12:05.721Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/bbf5c4a855371072bf3fec6e5074044f1b505b8856df343966c11e851eef3ab6 [none]
[debug] [2025-07-28T03:12:05.722Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/bbf5c4a855371072bf3fec6e5074044f1b505b8856df343966c11e851eef3ab6 [stream]
[debug] [2025-07-28T03:12:05.722Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/9563f2302179d3abf33dd49b6eab6595a1cdeb7d581ca034be150957a06529e3 [none]
[debug] [2025-07-28T03:12:05.722Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/9563f2302179d3abf33dd49b6eab6595a1cdeb7d581ca034be150957a06529e3 [stream]
[debug] [2025-07-28T03:12:05.722Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/86835ac53a0e1de04820388d01b501389d5e9311509282b5e3bfbfc752754083 [none]
[debug] [2025-07-28T03:12:05.722Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/86835ac53a0e1de04820388d01b501389d5e9311509282b5e3bfbfc752754083 [stream]
[debug] [2025-07-28T03:12:05.723Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/dac2436658d875db7a6d536198b657f5d79e97b575eb544cf45755323faea9b5 [none]
[debug] [2025-07-28T03:12:05.723Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/dac2436658d875db7a6d536198b657f5d79e97b575eb544cf45755323faea9b5 [stream]
[debug] [2025-07-28T03:12:05.723Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/ea6f82423f06ce8137da97f9d0abcd531bd812b9cc91f7ee8eb1d7387bb84ab3 [none]
[debug] [2025-07-28T03:12:05.723Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/ea6f82423f06ce8137da97f9d0abcd531bd812b9cc91f7ee8eb1d7387bb84ab3 [stream]
[debug] [2025-07-28T03:12:05.724Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/d7fecb9fd31a17180f5926be6a4c60f2dcfbd33b675a8df380cbb220bd398107 [none]
[debug] [2025-07-28T03:12:05.724Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/d7fecb9fd31a17180f5926be6a4c60f2dcfbd33b675a8df380cbb220bd398107 [stream]
[debug] [2025-07-28T03:12:05.724Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/748f1a863f072df2df18273ddcc6aadb6870bc7f1d120e5752aaadbfde581555 [none]
[debug] [2025-07-28T03:12:05.724Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/748f1a863f072df2df18273ddcc6aadb6870bc7f1d120e5752aaadbfde581555 [stream]
[debug] [2025-07-28T03:12:05.725Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/cd2992a875e4939d47289a3cc44c8b8a0e63817f4cc9fbd8b3bdf93b5f66fbb3 [none]
[debug] [2025-07-28T03:12:05.725Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/cd2992a875e4939d47289a3cc44c8b8a0e63817f4cc9fbd8b3bdf93b5f66fbb3 [stream]
[debug] [2025-07-28T03:12:05.725Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/c0e31b38c07fb2cac30269c70d55077e4f6a99e360e0c256f9b6f2d74adf24e4 [none]
[debug] [2025-07-28T03:12:05.725Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/c0e31b38c07fb2cac30269c70d55077e4f6a99e360e0c256f9b6f2d74adf24e4 [stream]
[debug] [2025-07-28T03:12:05.726Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/eb84f6b47de0b8f806623f3825462c9a1a67fe7c069c77c0b5130611eda45ee3 [none]
[debug] [2025-07-28T03:12:05.726Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/eb84f6b47de0b8f806623f3825462c9a1a67fe7c069c77c0b5130611eda45ee3 [stream]
[debug] [2025-07-28T03:12:05.726Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/42308c61be399a20fcfa0a7f987b2dbd21915b32d9244b8451493e87caeebaea [none]
[debug] [2025-07-28T03:12:05.726Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/42308c61be399a20fcfa0a7f987b2dbd21915b32d9244b8451493e87caeebaea [stream]
[debug] [2025-07-28T03:12:05.727Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/54a1304996fa75ed51569bae3a41842579ca44ec9073e12cf1c21b2d090c045b [none]
[debug] [2025-07-28T03:12:05.727Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/54a1304996fa75ed51569bae3a41842579ca44ec9073e12cf1c21b2d090c045b [stream]
[debug] [2025-07-28T03:12:05.727Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/ae5c06e2307aad7bf8fb02467ddd2ea6d7f40ea27915c5d9667f7632db6a0dbc [none]
[debug] [2025-07-28T03:12:05.727Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/speakoneai-dev-9f995/versions/b8a779513594bd0a/files/ae5c06e2307aad7bf8fb02467ddd2ea6d7f40ea27915c5d9667f7632db6a0dbc [stream]
[debug] [2025-07-28T03:12:05.728Z] [hosting][populate queue][FINAL] {"max":831,"min":831,"avg":831,"active":0,"complete":1,"success":1,"errored":0,"retried":0,"total":1,"elapsed":856}
[debug] [2025-07-28T03:12:05.728Z] [hosting] uploads queued: 43
