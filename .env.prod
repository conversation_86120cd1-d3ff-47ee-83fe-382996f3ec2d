# Production Environment Configuration for SpeakOneAI
NEXT_PUBLIC_FIREBASE_API_KEY="your-prod-firebase-api-key"
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN="speakoneai-prod.firebaseapp.com"
NEXT_PUBLIC_FIREBASE_PROJECT_ID="speakoneai-prod"
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET="speakoneai-prod.firebasestorage.app"
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID="your-prod-sender-id"
NEXT_PUBLIC_FIREBASE_APP_ID="your-prod-app-id"
GOOGLE_ANALYTICS_MEASUREMENT_ID="your-prod-ga-measurement-id"

# Google OAuth Configuration (Production)
GOOGLE_CLIENT_ID="your-prod-google-client-id"
GOOGLE_CLIENT_SECRET="your-prod-google-client-secret"

# Stripe Configuration (Production/Live)
STRIPE_PUBLISHABLE_KEY="pk_live_your_prod_stripe_publishable_key"
STRIPE_SECRET_KEY="sk_live_your_prod_stripe_secret_key"
STRIPE_WEBHOOK_SECRET="whsec_your_prod_webhook_secret"

NEXT_PUBLIC_SITE_URL="https://your-production-domain.com"
NEXT_PUBLIC_APP_NAME="SpeakOneAI"

NODE_ENV="production"

# Stripe Price IDs (Production)
STRIPE_PRICE_ID_STARTER_MONTHLY="price_prod_starter_monthly"
STRIPE_PRICE_ID_STARTER_YEARLY="price_prod_starter_yearly"
STRIPE_PRICE_ID_PRO_MONTHLY="price_prod_pro_monthly"
STRIPE_PRICE_ID_PRO_YEARLY="price_prod_pro_yearly"
STRIPE_PRICE_ID_PREMIUM_MONTHLY="price_prod_premium_monthly"
STRIPE_PRICE_ID_PREMIUM_YEARLY="price_prod_premium_yearly"
STRIPE_PRICE_ID_MAX_MONTHLY="price_prod_max_monthly"
STRIPE_PRICE_ID_MAX_YEARLY="price_prod_max_yearly"

# Firebase Admin (Server-side) - Production
# Note: These are placeholder values. Replace with actual Firebase Admin SDK credentials
# FIREBASE_PROJECT_ID="speakoneai-prod"
# FIREBASE_CLIENT_EMAIL="<EMAIL>"
# FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-actual-private-key\n-----END PRIVATE KEY-----\n"

# Backend API URL (Production)
BACKEND_API_URL="https://asia-east1-speakoneai-prod.cloudfunctions.net/backend-api"
