// Subscription plans configuration (client-safe)
// REVIEW Subscription.md for the setup

export type SubscriptionPlan = 'FREE' | 'STARTER' | 'PRO' | 'PREMIUM' | 'MAX'

export const SUBSCRIPTION_PLANS = {
  FREE: {
    name: 'Free',
    description: '10 minutes per day',
    price: 0,
    priceId: null, // Free plan doesn't need Stripe price ID
    dailyLimitSeconds: 600, // 10 minutes = 600 seconds
    platforms: ['windows', 'macos', 'ios', 'android'] as const,
    maxDevices: 1,
    features: [
      '10 minutes per day',
      'Access to all platforms (Windows, macOS, iOS, Android)',
      'Maximum device limit: 1'
    ]
  },
  STARTER: {
    name: 'Starter',
    description: '1 hour per day',
    monthlyPrice: 999, // $9.99 in cents
    yearlyPrice: 9900, // $99.00 in cents
    monthlyPriceId: process.env.STRIPE_PRICE_ID_STARTER_MONTHLY!,
    yearlyPriceId: process.env.STRIPE_PRICE_ID_STARTER_YEARLY!,
    dailyLimitSeconds: 3600, // 1 hour = 3600 seconds
    platforms: ['windows', 'macos', 'ios', 'android'] as const,
    maxDevices: 2,
    features: [
      '1 hour per day',
      'Access to all platforms (Windows, macOS, iOS, Android)',
      'Maximum device limit: 2'
    ]
  },
  PRO: {
    name: 'Pro',
    description: '3 hours per day',
    monthlyPrice: 1999, // $19.99 in cents
    yearlyPrice: 19900, // $199.00 in cents
    monthlyPriceId: process.env.STRIPE_PRICE_ID_PRO_MONTHLY!,
    yearlyPriceId: process.env.STRIPE_PRICE_ID_PRO_YEARLY!,
    dailyLimitSeconds: 10800, // 3 hours = 10800 seconds
    platforms: ['windows', 'macos', 'ios', 'android'] as const,
    maxDevices: 3,
    features: [
      '3 hours per day',
      'Access to all platforms (Windows, macOS, iOS, Android)',
      'Maximum device limit: 3'
    ]
  },
  PREMIUM: {
    name: 'Premium',
    description: '8 hours per day',
    monthlyPrice: 5999, // $59.99 in cents
    yearlyPrice: 55900, // $559.00 in cents
    monthlyPriceId: process.env.STRIPE_PRICE_ID_PREMIUM_MONTHLY!,
    yearlyPriceId: process.env.STRIPE_PRICE_ID_PREMIUM_YEARLY!,
    dailyLimitSeconds: 28800, // 8 hours = 28800 seconds
    platforms: ['windows', 'macos', 'ios', 'android'] as const,
    maxDevices: 5,
    features: [
      '8 hours per day',
      'Access to all platforms (Windows, macOS, iOS, Android)',
      'Maximum device limit: 5'
    ]
  },
  MAX: {
    name: 'Max',
    description: 'Unlimited usage',
    monthlyPrice: 12999, // $129.99 in cents
    yearlyPrice: 109900, // $1099.00 in cents
    monthlyPriceId: process.env.STRIPE_PRICE_ID_MAX_MONTHLY!,
    yearlyPriceId: process.env.STRIPE_PRICE_ID_MAX_YEARLY!,
    dailyLimitSeconds: -1, // -1 means unlimited
    platforms: ['windows', 'macos', 'ios', 'android'] as const,
    maxDevices: -1, // -1 means unlimited
    features: [
      'Unlimited usage',
      'Access to all platforms (Windows, macOS, iOS, Android)',
      'Unlimited device limit'
    ]
  }
} as const

// Helper functions
export function getYearlySavingsPercentage(plan: SubscriptionPlan): number {
  if (plan === 'FREE') return 0
  
  const planConfig = SUBSCRIPTION_PLANS[plan]
  const monthlyTotal = planConfig.monthlyPrice * 12
  const yearlyPrice = planConfig.yearlyPrice
  const savings = monthlyTotal - yearlyPrice
  
  return Math.round((savings / monthlyTotal) * 100)
}

// Get price ID based on plan and billing period
export function getPriceId(plan: SubscriptionPlan, billingPeriod: 'monthly' | 'yearly'): string | null {
  if (plan === 'FREE') return null

  const planConfig = SUBSCRIPTION_PLANS[plan]
  return billingPeriod === 'monthly' ? planConfig.monthlyPriceId : planConfig.yearlyPriceId
}

// Get price amount based on plan and billing period
export function getPriceAmount(plan: SubscriptionPlan, billingPeriod: 'monthly' | 'yearly'): number {
  if (plan === 'FREE') return 0

  const planConfig = SUBSCRIPTION_PLANS[plan]
  return billingPeriod === 'monthly' ? planConfig.monthlyPrice : planConfig.yearlyPrice
}

// Check if user can access a specific platform
export function canAccessPlatform(userPlan: SubscriptionPlan, platform: 'windows' | 'macos' | 'ios' | 'android'): boolean {
  const planConfig = SUBSCRIPTION_PLANS[userPlan]
  return (planConfig.platforms as readonly string[]).includes(platform)
}
