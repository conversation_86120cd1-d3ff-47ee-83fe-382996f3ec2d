'use client'

import { useEffect, useState, Suspense, useCallback } from 'react'
import { useSearchParams } from 'next/navigation'
import { onAuthStateChanged, User as FirebaseUser } from 'firebase/auth'
import { auth } from '@/lib/firebase'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import {
  Download,
  CheckCircle,
  Calendar,
  CreditCard,
  User,
  ExternalLink,
  Smartphone,
  Monitor,
  Clock,
  Settings,
  Trash2,
  RefreshCw,
  Activity,
  Shield,
  Zap
} from 'lucide-react'
import { Purchase } from '@/lib/db'
import { PRODUCTS } from '@/lib/stripe'
import { useCountdownTimer } from '@/hooks/useCountdownTimer'

// Mock data - replace with actual API calls
interface Device {
  id: string
  name: string
  type: 'windows' | 'macos' | 'ios' | 'android'
  lastActive: Date
  location?: string
}

interface UsageData {
  dailyUsedSeconds: number
  dailyLimitSeconds: number
  resetTime: Date | string  // Can be Date object or ISO string
  currentPlan: string
}

function DashboardContent() {
  const [user, setUser] = useState<FirebaseUser | null>(null)
  const [authLoading, setAuthLoading] = useState(true)
  const searchParams = useSearchParams()
  const [purchases, setPurchases] = useState<Purchase[]>([])
  const [loading, setLoading] = useState(true)
  const [showSuccess, setShowSuccess] = useState(false)

  // Real state for dashboard features
  const [devices, setDevices] = useState<Device[]>([])
  const [usageData, setUsageData] = useState<UsageData | null>(null)
  const [userProfile, setUserProfile] = useState<any>(null)

  // Countdown timer for daily reset
  const resetTime = usageData?.resetTime
  const { timeLeft, formatTime, formatTimeWithSeconds } = useCountdownTimer({
    resetTime,
    onReset: useCallback(() => {
      // Refresh usage data when timer resets
      if (user) {
        fetchDashboardData()
      }
    }, [user])
  })

  // Firebase auth state listener
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setUser(user)
      setAuthLoading(false)
    })

    return () => unsubscribe()
  }, [])

  // Check for success parameter
  useEffect(() => {
    if (searchParams.get('success') === 'true') {
      setShowSuccess(true)
      // Remove success parameter from URL
      const url = new URL(window.location.href)
      url.searchParams.delete('success')
      url.searchParams.delete('product')
      window.history.replaceState({}, '', url.toString())
    }
  }, [searchParams])

  // Fetch all user data using unified dashboard API
  useEffect(() => {
    if (user) {
      fetchDashboardData()
    }
  }, [user])

  // Fetch unified dashboard data
  const fetchDashboardData = useCallback(async () => {
    if (!user) return

    try {
      console.log('🔄 Starting dashboard data fetch...')

      // 只使用 Firebase SDK，不使用回退機制
      console.log('🔄 Calling Firebase SDK...')
      const { firebaseApi } = await import('@/lib/firebase-api')
      const result = await firebaseApi.getUserDashboard()

      if (result.success) {
        console.log('✅ Firebase SDK returned success')
        processResponseData(result, 'Firebase SDK')
      } else {
        console.error('❌ Firebase SDK returned error:', result)
        throw new Error('Firebase SDK failed')
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    }
  }, [user, processResponseData])

  // 統一的數據處理函數
  const processResponseData = useCallback((result: any, source: string) => {
    console.log(`🔍 Processing response data from ${source}:`)
    console.log('  - Response structure:', {
      success: result.success,
      hasData: !!result.data,
      dataKeys: result.data ? Object.keys(result.data) : []
    })

    if (result.data) {
      // Set user profile - map API V2.3 structure to frontend format
      setUserProfile({
        id: result.data.user?.user_id || 'unknown',
        email: result.data.user?.email || 'unknown',
        name: result.data.user?.display_name || 'Unknown User',
        image: result.data.user?.profile_image || '',
        subscriptionPlan: result.data.subscription?.plan || 'FREE',
        subscriptionStatus: result.data.subscription?.status || 'inactive',
        dailyUsageSeconds: result.data.subscription?.current_day_used_seconds || 0,
        dailyLimitSeconds: result.data.subscription?.daily_limit_seconds || 3600,
        lastUsageReset: new Date().toISOString(),
        createdAt: result.data.user?.created_at || new Date().toISOString(),
        stripeCustomerId: result.data.subscription?.stripe_customer_id || null,
        stripeSubscriptionId: result.data.subscription?.stripe_subscription_id || null,
        subscriptionEndDate: result.data.subscription?.next_billing_date || null,
        preferences: result.data.user?.preferences || {}
      })

      // Set usage data - V2.3 只使用 subscription 數據
      const subscription = result.data.subscription
      const dailyUsed = subscription?.current_day_used_seconds || 0
      const dailyLimit = subscription?.daily_limit_seconds || 3600

      setUsageData({
        dailyUsedSeconds: dailyUsed,
        dailyLimitSeconds: dailyLimit,
        resetTime: new Date(new Date().setHours(24, 0, 0, 0)).toISOString(),
        currentPlan: subscription?.plan || 'FREE'
      })

      console.log('📊 V2.3 Usage calculation:', {
        dailyUsed,
        dailyLimit,
        remaining: dailyLimit === -1 ? -1 : Math.max(0, dailyLimit - dailyUsed),
        percentage: dailyLimit > 0 ? (dailyUsed / dailyLimit * 100).toFixed(1) + '%' : '0%',
        canUse: dailyLimit === -1 || dailyUsed < dailyLimit
      })

      // Set devices - map API V2.3 structure
      const activeDevices = result.data.devices?.active_devices || []
      const transformedDevices = activeDevices.map((device: any) => ({
        id: device.device_id || 'unknown',
        name: device.device_name || `${device.platform || 'Unknown'} Device`,
        type: device.platform || 'unknown',
        lastActive: device.last_active_at ? new Date(device.last_active_at) : new Date(),
        location: 'Unknown',
        status: device.status || 'unknown',
        isAuthorized: device.is_authorized || false,
        appVersion: device.app_version || 'Unknown'
      }))

      setDevices(transformedDevices)

      console.log(`✅ Dashboard data loaded successfully (${source})`)
      console.log('📊 User profile:', result.data.user)
      console.log('📋 Subscription:', result.data.subscription)
      console.log('📱 Devices:', transformedDevices)
      console.log('📈 Usage:', result.data.usage)
    }
  }, [])

  const handleDownload = (downloadUrl: string, _productName: string) => {
    // In a real app, you might want to track downloads or provide secure download links
    window.open(downloadUrl, '_blank')
  }

  const handleRemoveDevice = async (deviceId: string) => {
    if (!user) return

    // Confirm removal
    const deviceToRemove = devices.find(d => d.id === deviceId)
    const deviceName = deviceToRemove?.name || 'this device'

    if (!confirm(`Are you sure you want to remove "${deviceName}"? This action cannot be undone.`)) {
      return
    }

    try {
      // 使用 Firebase SDK 替代手動 HTTP 調用
      const { firebaseApi } = await import('@/lib/firebase-api')
      const result = await firebaseApi.removeDevice({
        device_id: deviceId,
        reason: 'user_request'
      })

      if (result.success) {
        // Remove device from local state
        setDevices(devices.filter(d => d.id !== deviceId))
        console.log('✅ Device removed successfully (Firebase SDK)')

        // Refresh dashboard data to get updated device count
        fetchDashboardData()
      } else {
        console.error('Failed to remove device:', result)
        alert('Failed to remove device. Please try again.')
      }
    } catch (error) {
      console.error('Error removing device (Firebase SDK):', error)
      alert('Failed to remove device. Please try again.')
    }
  }

  const handleUpgradeOrManagePlan = async () => {
    if (!user) return

    try {
      const idToken = await user.getIdToken()

      // Check if user has an active subscription
      if (userProfile?.stripeCustomerId && userProfile?.subscriptionPlan !== 'FREE') {
        // User has subscription, redirect to customer portal
        const response = await fetch('/api/stripe/customer-portal', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${idToken}`,
          },
          body: JSON.stringify({
            returnUrl: window.location.href
          }),
        })

        if (response.ok) {
          const result = await response.json()
          if (result.success) {
            // Redirect to Stripe customer portal
            window.location.href = result.url
          } else {
            console.error('Failed to create customer portal session:', result.error)
            alert('Failed to open billing portal. Please try again.')
          }
        } else {
          console.error('Failed to create customer portal session:', response.status)
          alert('Failed to open billing portal. Please try again.')
        }
      } else {
        // User doesn't have subscription, redirect to pricing page
        window.location.href = '/pricing'
      }
    } catch (error) {
      console.error('Error handling upgrade/manage plan:', error)
      alert('Failed to process request. Please try again.')
    }
  }

  // Helper function to format seconds to time string (for usage display)
  const formatUsageTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }



  const getDeviceIcon = (type: Device['type']) => {
    switch (type) {
      case 'windows':
      case 'macos':
        return <Monitor className="w-5 h-5" />
      case 'ios':
      case 'android':
        return <Smartphone className="w-5 h-5" />
      default:
        return <Monitor className="w-5 h-5" />
    }
  }

  const getUsagePercentage = () => {
    if (!usageData || usageData.dailyLimitSeconds === -1) return 0 // Unlimited
    return (usageData.dailyUsedSeconds / usageData.dailyLimitSeconds) * 100
  }

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="p-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-4">Please sign in to view your dashboard.</p>
          <Button onClick={() => window.location.href = '/'}>
            Go to Home
          </Button>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Success Message */}
        {showSuccess && (
          <div className="mb-8 bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 text-green-600 mr-3" />
              <div>
                <h3 className="text-green-800 font-medium">Payment Successful!</h3>
                <p className="text-green-700 text-sm">
                  Your purchase has been completed. You can now download your software below.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome back, {user.displayName || user.email}!
          </h1>
          <p className="text-gray-600">
            Manage your SpeakOneAI purchases and downloads
          </p>
        </div>

        {/* Usage Overview Cards */}
        {usageData ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Daily Usage */}
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <Activity className="w-5 h-5 text-blue-600 mr-2" />
                  <h3 className="font-semibold text-gray-900">Daily Usage</h3>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Used today</span>
                  <span className="font-medium">{formatUsageTime(usageData?.dailyUsedSeconds || 0)}</span>
                </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${
                    getUsagePercentage() > 90 ? 'bg-red-500' :
                    getUsagePercentage() > 70 ? 'bg-yellow-500' : 'bg-blue-500'
                  }`}
                  style={{ width: `${Math.min(getUsagePercentage(), 100)}%` }}
                ></div>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">
                  {usageData?.dailyLimitSeconds === -1 ? 'Unlimited' : formatUsageTime((usageData?.dailyLimitSeconds || 0) - (usageData?.dailyUsedSeconds || 0))} left
                </span>
                <span className="text-gray-600">
                  {usageData?.dailyLimitSeconds === -1 ? '∞' : formatUsageTime(usageData?.dailyLimitSeconds || 0)}
                </span>
              </div>
            </div>
          </Card>

          {/* Reset Timer */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <RefreshCw className="w-5 h-5 text-green-600 mr-2" />
                <h3 className="font-semibold text-gray-900">Daily Reset</h3>
              </div>
            </div>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-gray-900">{formatTime(timeLeft)}</div>
              <div className="text-sm text-gray-600">Until limit refreshes</div>
              <div className="text-xs text-gray-500">
                Resets at midnight (12:00 AM)
              </div>
            </div>
          </Card>

          {/* Current Plan */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Zap className="w-5 h-5 text-purple-600 mr-2" />
                <h3 className="font-semibold text-gray-900">Current Plan</h3>
              </div>
            </div>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-gray-900">{usageData?.currentPlan || 'Loading...'}</div>
              <div className="text-sm text-gray-600">
                {usageData?.dailyLimitSeconds === -1 ? 'Unlimited usage' : `${formatUsageTime(usageData?.dailyLimitSeconds || 0)} per day`}
              </div>
              <Button
                variant="outline"
                size="sm"
                className="w-full mt-2"
                onClick={handleUpgradeOrManagePlan}
              >
                {userProfile?.subscriptionPlan === 'FREE' ? 'Upgrade Plan' : 'Manage Plan'}
              </Button>
            </div>
          </Card>

          {/* Active Devices */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Shield className="w-5 h-5 text-orange-600 mr-2" />
                <h3 className="font-semibold text-gray-900">Devices</h3>
              </div>
            </div>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-gray-900">{devices.length}</div>
              <div className="text-sm text-gray-600">Active devices</div>
            </div>
          </Card>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-full"></div>
                </div>
              </Card>
            ))}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Device Management */}
          <div className="lg:col-span-1">
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <Settings className="w-5 h-5 text-gray-600 mr-2" />
                  <h2 className="text-lg font-semibold text-gray-900">Device Management</h2>
                </div>
              </div>

              {/* Device Limit Warning */}
              {userProfile && devices.length > (userProfile.subscriptionPlan === 'FREE' ? 1 : 5) && (
                <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <svg className="w-5 h-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">
                        Device Limit Exceeded
                      </h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>
                          You have {devices.length} active devices, but your {userProfile.subscriptionPlan} plan only allows {userProfile.subscriptionPlan === 'FREE' ? 1 : 5} device{userProfile.subscriptionPlan === 'FREE' ? '' : 's'}.
                        </p>
                        <p className="mt-1">
                          Please remove {devices.length - (userProfile.subscriptionPlan === 'FREE' ? 1 : 5)} device{devices.length - (userProfile.subscriptionPlan === 'FREE' ? 1 : 5) > 1 ? 's' : ''} to continue using the service.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div className="space-y-4">
                {devices.map((device) => (
                  <div key={device.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="text-gray-600">
                        {getDeviceIcon(device.type)}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 text-sm">{device.name}</p>
                        <p className="text-xs text-gray-600">
                          {device.lastActive.toLocaleDateString()} • {device.location}
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveDevice(device.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
                {devices.length === 0 && (
                  <div className="text-center py-4 text-gray-500 text-sm">
                    No devices connected
                  </div>
                )}
              </div>
            </Card>
          </div>

          {/* Account & Subscription Info */}
          <div className="lg:col-span-2 space-y-6">
            {/* Account Info */}
            <Card className="p-6">
              <div className="flex items-center mb-4">
                <User className="w-5 h-5 text-gray-600 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">Account Information</h2>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center space-x-4">
                  {user.photoURL ? (
                    <img
                      src={user.photoURL}
                      alt={user.displayName || 'User'}
                      className="w-16 h-16 rounded-full"
                    />
                  ) : (
                    <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center">
                      <User className="w-8 h-8 text-gray-600" />
                    </div>
                  )}
                  <div>
                    <p className="font-semibold text-gray-900 text-lg">{user.displayName || 'User'}</p>
                    <p className="text-gray-600">{user.email}</p>
                    <p className="text-sm text-gray-500 mt-1">
                      <Calendar className="w-4 h-4 inline mr-1" />
                      Member since {new Date().toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Current Plan:</span>
                    <span className="font-medium text-gray-900">{usageData?.currentPlan || 'Loading...'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Daily Limit:</span>
                    <span className="font-medium text-gray-900">
                      {usageData?.dailyLimitSeconds === -1 ? 'Unlimited' : formatUsageTime(usageData?.dailyLimitSeconds || 0)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Active Devices:</span>
                    <span className="font-medium text-gray-900">{devices.length}</span>
                  </div>
                </div>
              </div>
            </Card>

            {/* Downloads */}
            <Card className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <Download className="w-5 h-5 text-gray-600 mr-2" />
                  <h2 className="text-lg font-semibold text-gray-900">Download SpeakOneAI</h2>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Windows Download */}
                <div className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-center space-x-3 mb-3">
                    <Monitor className="w-8 h-8 text-blue-600" />
                    <div>
                      <h3 className="font-semibold text-gray-900">Windows</h3>
                      <p className="text-sm text-gray-600">Windows 10/11</p>
                    </div>
                  </div>
                  <Button className="w-full" onClick={() => handleDownload('#', 'Windows')}>
                    <Download className="w-4 h-4 mr-2" />
                    Download for Windows
                  </Button>
                </div>

                {/* macOS Download */}
                <div className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-center space-x-3 mb-3">
                    <Monitor className="w-8 h-8 text-gray-600" />
                    <div>
                      <h3 className="font-semibold text-gray-900">macOS</h3>
                      <p className="text-sm text-gray-600">macOS 10.15+</p>
                    </div>
                  </div>
                  <Button className="w-full" onClick={() => handleDownload('#', 'macOS')}>
                    <Download className="w-4 h-4 mr-2" />
                    Download for macOS
                  </Button>
                </div>
              </div>

              {/* Mobile Apps Coming Soon */}
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Mobile Apps Coming Soon</h4>
                <div className="flex space-x-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <Smartphone className="w-4 h-4 mr-1" />
                    iOS App
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Smartphone className="w-4 h-4 mr-1" />
                    Android App
                  </div>
                </div>
              </div>
            </Card>

            {/* Subscription History */}
            <Card className="p-6">
              <div className="flex items-center mb-6">
                <CreditCard className="w-5 h-5 text-gray-600 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">Subscription History</h2>
              </div>

              {loading ? (
                <div className="text-center py-8">
                  <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                  <p className="text-gray-600">Loading your purchases...</p>
                </div>
              ) : purchases.length === 0 ? (
                <div className="text-center py-8">
                  <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No purchases yet</h3>
                  <p className="text-gray-600 mb-4">
                    You haven&apos;t made any purchases yet. Get started with SpeakOneAI today!
                  </p>
                  <Button asChild>
                    <a href="/pricing">View Pricing</a>
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {purchases.map((purchase) => {
                    // Handle different product types
                    const product = purchase.productType === 'SUBSCRIPTION'
                      ? { name: 'Subscription', description: 'Monthly/Annual subscription' }
                      : purchase.productType === 'CREDITS'
                      ? { name: 'Credits', description: 'Usage credits' }
                      : null

                    if (!product) return null

                    return (
                      <div
                        key={purchase.id}
                        className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <h3 className="font-medium text-gray-900 mb-1">
                              {product.name}
                            </h3>
                            <p className="text-sm text-gray-600 mb-2">
                              Purchased on {purchase.createdAt.toDate().toLocaleDateString()}
                            </p>
                            <div className="flex items-center">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                purchase.status === 'completed'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {purchase.status === 'completed' ? 'Completed' : 'Pending'}
                              </span>
                              <span className="ml-3 text-sm text-gray-600">
                                ${(purchase.amount / 100).toFixed(2)}
                              </span>
                            </div>
                          </div>

                          {purchase.status === 'completed' && purchase.productType === 'SUBSCRIPTION' && (
                            <div className="text-sm text-green-600">
                              ✓ Active subscription
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </Card>
          </div>
        </div>

        {/* Support Section */}
        <div className="mt-8">
          <Card className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Need Help?</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <h3 className="font-medium text-gray-900 mb-2">Documentation</h3>
                <p className="text-sm text-gray-600 mb-3">
                  Learn how to get the most out of SpeechPilot
                </p>
                <Button variant="outline" size="sm">
                  View Docs
                </Button>
              </div>
              <div className="text-center">
                <h3 className="font-medium text-gray-900 mb-2">Support</h3>
                <p className="text-sm text-gray-600 mb-3">
                  Get help from our support team
                </p>
                <Button variant="outline" size="sm">
                  Contact Support
                </Button>
              </div>
              <div className="text-center">
                <h3 className="font-medium text-gray-900 mb-2">Community</h3>
                <p className="text-sm text-gray-600 mb-3">
                  Join our user community
                </p>
                <Button variant="outline" size="sm">
                  Join Forum
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default function DashboardPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
      <DashboardContent />
    </Suspense>
  )
}
