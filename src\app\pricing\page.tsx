'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { CheckCircle, Download, Star, Zap, ChevronDown, ChevronUp } from 'lucide-react'
import { SUBSCRIPTION_PLANS, SubscriptionPlan, getYearlySavingsPercentage } from '@/lib/subscription-plans'
import { auth } from '@/lib/firebase'
import { onAuthStateChanged, User } from 'firebase/auth'
import SSOModal from '@/components/auth/SSOModal'

// FAQ Data
const faqs = [
  {
    question: "What's included in the free plan?",
    answer: "The free plan includes 5 minutes of speech-to-text conversion per day with basic features. Perfect for trying out our service."
  },
  {
    question: "Can I cancel my subscription anytime?",
    answer: "Yes, you can cancel your subscription at any time. Your access will continue until the end of your current billing period."
  },
  {
    question: "What platforms are supported?",
    answer: "SpeakOneAI is available on Windows, macOS, iOS, and Android. We also offer a web version that works on any modern browser."
  },
  {
    question: "Is my data secure?",
    answer: "Absolutely. We use enterprise-grade encryption and never store your audio files permanently. All processing is done securely and your privacy is our top priority."
  },
  {
    question: "Do you offer refunds?",
    answer: "Yes, we offer a 30-day money-back guarantee for all paid plans. If you're not satisfied, contact our support team for a full refund."
  },
  {
    question: "Can I upgrade or downgrade my plan?",
    answer: "Yes, you can change your plan at any time. Upgrades take effect immediately, while downgrades take effect at the next billing cycle."
  }
]

// FAQ Item Component
function FAQItem({ question, answer }: { question: string; answer: string }) {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div className="border border-gray-200 rounded-lg">
      <button
        className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="font-medium text-gray-900">{question}</span>
        {isOpen ? (
          <ChevronUp className="w-5 h-5 text-gray-500" />
        ) : (
          <ChevronDown className="w-5 h-5 text-gray-500" />
        )}
      </button>
      {isOpen && (
        <div className="px-6 pb-4">
          <p className="text-gray-600">{answer}</p>
        </div>
      )}
    </div>
  )
}

export default function PricingPage() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState<string | null>(null)
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly')
  const [showSSOModal, setShowSSOModal] = useState(false)

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setUser(user)
    })
    return () => unsubscribe()
  }, [])

  const handleSubscribe = async (planId: SubscriptionPlan) => {
    if (planId === 'FREE') {
      // Handle free plan signup
      if (!user) {
        setShowSSOModal(true)
      }
      return
    }

    if (!user) {
      setShowSSOModal(true)
      return
    }

    setLoading(planId)

    try {
      // Get Firebase ID token
      const idToken = await user.getIdToken()

      const response = await fetch('/api/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`,
        },
        body: JSON.stringify({
          planId,
          billingPeriod,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session')
      }

      if (data.url) {
        window.location.href = data.url
      } else {
        throw new Error('No checkout URL received')
      }
    } catch (error) {
      console.error('Subscription error:', error)
      alert('Failed to start checkout. Please try again.')
    } finally {
      setLoading(null)
    }
  }

  const plans = [
    {
      key: 'FREE' as SubscriptionPlan,
      ...SUBSCRIPTION_PLANS.FREE,
      icon: <Star className="w-8 h-8 text-gray-400" />,
      popular: false
    },
    {
      key: 'STARTER' as SubscriptionPlan,
      ...SUBSCRIPTION_PLANS.STARTER,
      icon: <Download className="w-8 h-8 text-blue-600" />,
      popular: false
    },
    {
      key: 'PRO' as SubscriptionPlan,
      ...SUBSCRIPTION_PLANS.PRO,
      icon: <Zap className="w-8 h-8 text-purple-600" />,
      popular: true
    },
    {
      key: 'PREMIUM' as SubscriptionPlan,
      ...SUBSCRIPTION_PLANS.PREMIUM,
      icon: <Star className="w-8 h-8 text-yellow-500" />,
      popular: false
    },
    {
      key: 'MAX' as SubscriptionPlan,
      ...SUBSCRIPTION_PLANS.MAX,
      icon: <CheckCircle className="w-8 h-8 text-green-600" />,
      popular: false
    }
  ]

  const getPrice = (plan: SubscriptionPlan) => {
    if (plan === 'FREE') return 0
    const planConfig = SUBSCRIPTION_PLANS[plan]
    return billingPeriod === 'monthly' ? planConfig.monthlyPrice : planConfig.yearlyPrice
  }

  const getDisplayPrice = (plan: SubscriptionPlan) => {
    const price = getPrice(plan)
    if (plan === 'FREE') return 'Free'
    return `$${(price / 100).toFixed(2)}`
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Choose Your Plan
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Professional speech-to-text software with flexible subscription plans.
            Start free and upgrade anytime.
          </p>

          {/* Billing Toggle with Badge */}
          <div className="flex items-center justify-center mb-8">
            <div className="relative">
              {/* Toggle container */}
              <div className="flex items-center space-x-4">
                <span className="text-gray-900 font-medium w-16 text-right">
                  Monthly
                </span>
                <button
                  onClick={() => setBillingPeriod(billingPeriod === 'monthly' ? 'yearly' : 'monthly')}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                    billingPeriod === 'yearly' ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      billingPeriod === 'yearly' ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
                <span className="text-gray-900 font-medium w-16 text-left">
                  Annual
                </span>
              </div>

              {/* Modern Save Badge */}
              <div className="absolute -right-28 top-1/3 transform -translate-y-1/2">
                <div className="relative">
                  {/* Main badge */}
                  <div className="bg-gradient-to-r from-orange-400 via-red-500 to-pink-500 text-white px-3 py-1.5 rounded-lg shadow-lg transform rotate-3 hover:rotate-0 transition-transform duration-300">
                    <div className="flex items-center space-x-1">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z" clipRule="evenodd" />
                      </svg>
                      <span className="text-xs font-bold tracking-wide">SAVE 30%</span>
                    </div>
                  </div>

                  {/* Subtle glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-orange-400 via-red-500 to-pink-500 rounded-lg blur-sm opacity-30 -z-10"></div>

                  {/* Small indicator pointing to Annual */}
                  <div className="absolute -left-2 top-1/2 transform -translate-y-1/2">
                    <div className="w-0 h-0 border-t-4 border-b-4 border-r-6 border-transparent border-r-red-500"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6 mb-16">
          {plans.map((plan) => (
            <Card
              key={plan.key}
              className={`relative p-6 transition-all duration-300 hover:shadow-lg h-full flex flex-col ${
                plan.popular
                  ? 'ring-2 ring-purple-500 shadow-xl scale-105 bg-gradient-to-br from-purple-50 to-blue-50'
                  : 'shadow-md hover:shadow-lg'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                  <span className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg whitespace-nowrap">
                    <Star className="w-3 h-3 inline mr-1" />
                    Most Popular
                  </span>
                </div>
              )}

              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {plan.name}
                </h3>
                <p className="text-gray-600 text-sm mb-4">{plan.description}</p>
                <div className="mb-4">
                  <span className="text-3xl font-bold text-gray-900">
                    {getDisplayPrice(plan.key)}
                  </span>
                  {plan.key !== 'FREE' && (
                    <span className="text-gray-500 text-sm ml-1">
                      /{billingPeriod === 'monthly' ? 'mo' : 'yr'}
                    </span>
                  )}
                </div>
                {plan.key !== 'FREE' && billingPeriod === 'yearly' && (
                  <p className="text-green-600 font-medium text-xs">
                    Save {getYearlySavingsPercentage(plan.key)}% vs monthly
                  </p>
                )}
              </div>

              <ul className="space-y-2 mb-6 text-sm flex-grow">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-600">{feature}</span>
                  </li>
                ))}
              </ul>

              <div className="mt-auto">
                <Button
                  onClick={() => handleSubscribe(plan.key)}
                  disabled={loading === plan.key}
                  className={`w-full ${
                    plan.popular
                      ? 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700'
                      : plan.key === 'FREE'
                      ? 'bg-gray-600 hover:bg-gray-700'
                      : ''
                  }`}
                  size="sm"
                >
                {loading === plan.key ? (
                  <div className="flex items-center">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Processing...
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    {plan.key === 'FREE' ? (
                      <>
                        <Star className="w-4 h-4 mr-2" />
                        {user ? 'Current Plan' : 'Get Started'}
                      </>
                    ) : (
                      <>
                        <Zap className="w-4 h-4 mr-2" />
                        Subscribe
                      </>
                    )}
                  </div>
                )}
              </Button>
              </div>
            </Card>
          ))}
        </div>



        {/* FAQ Section */}
        <div className="rounded-lg">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">
            Frequently Asked Questions
          </h2>
          <div className="max-w-3xl mx-auto space-y-4">
            {faqs.map((faq, index) => (
              <FAQItem key={index} question={faq.question} answer={faq.answer} />
            ))}
          </div>
        </div>
      </div>

      {/* SSO Modal */}
      <SSOModal
        isOpen={showSSOModal}
        onClose={() => setShowSSOModal(false)}
        onSuccess={() => {
          setShowSSOModal(false)
          // User will be automatically updated via onAuthStateChanged
        }}
      />
    </div>
  )
}
