'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import Link from 'next/link'
import {
  Mic,
  Download,
  Zap,
  Shield,
  Globe,
  Clock,
  CheckCircle,
  Star
} from 'lucide-react'
import SSOModal from '@/components/auth/SSOModal'
import { useState } from 'react'

export default function HomePage() {
  const [showSSOModal, setShowSSOModal] = useState(false)
  const features = [
    {
      icon: <Mic className="w-6 h-6" />,
      title: "Advanced Speech Recognition",
      description: "Industry-leading accuracy with support for multiple languages and accents"
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Real-time Processing",
      description: "Instant transcription as you speak with minimal latency"
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Privacy First",
      description: "All processing happens locally on your device - your data never leaves your computer"
    },
    {
      icon: <Globe className="w-6 h-6" />,
      title: "Multi-language Support",
      description: "Support for 50+ languages with automatic language detection"
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: "Productivity Boost",
      description: "Type 3x faster than traditional typing with voice commands and shortcuts"
    },
    {
      icon: <Download className="w-6 h-6" />,
      title: "Easy Integration",
      description: "Works seamlessly with your favorite applications and workflows"
    }
  ]

  const testimonials = [
    {
      name: "Sarah Johnson",
      role: "Content Writer",
      content: "SpeechPilot has revolutionized my writing process. I can now create content 3x faster!",
      rating: 5
    },
    {
      name: "Michael Chen",
      role: "Software Developer",
      content: "Perfect for code documentation and meeting notes. The accuracy is incredible.",
      rating: 5
    },
    {
      name: "Emily Rodriguez",
      role: "Journalist",
      content: "Essential tool for interviews and article writing. Saves me hours every day.",
      rating: 5
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              No More Typing - Just Speak on
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                {" "}All Your Apps on Every Device
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              AI-Powered speech-to-text software for IOS, Android, Windows, macOS. 
              Experience industry-leading accuracy, real-time processing, and complete privacy.
            </p>
            {/* Download Section - Modern Enterprise Style */}
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-gray-800 mb-8 text-center">Download and Get Started For Free</h3>

              {/* Primary Downloads - Horizontal Layout */}
              <div className="flex flex-wrap justify-center gap-4 mb-6">
                {/* Windows Download */}
                <a
                  href="#"
                  className="group relative inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 min-w-[180px]"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-white/15 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M3 12V6.75l6-1.32v6.48L3 12zm17-9v8.75l-10 .15V5.21L20 3zM3 13l6 .09v6.81l-6-1.21V13zm17 .25V22l-10-1.91V13.1l10 .15z"/>
                      </svg>
                    </div>
                    <div className="text-left">
                      <div className="text-sm text-blue-100">Download for</div>
                      <div className="text-xl font-bold leading-tight">Windows</div>
                    </div>
                  </div>
                </a>

                {/* macOS Download */}
                <a
                  href="#"
                  className="group relative inline-flex items-center px-8 py-4 bg-gradient-to-r from-gray-800 to-gray-900 text-white rounded-xl hover:from-gray-900 hover:to-black transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 min-w-[180px]"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-white/15 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                      </svg>
                    </div>
                    <div className="text-left">
                      <div className="text-sm text-gray-300">Download for</div>
                      <div className="text-xl font-bold leading-tight">macOS</div>
                    </div>
                  </div>
                </a>
              </div>

              {/* Coming Soon - Compact Horizontal Layout */}
              <div className="flex flex-wrap justify-center gap-3">
                <div className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-600 rounded-lg border border-gray-200">
                  <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                  </svg>
                  <span className="text-sm font-medium">iOS - Coming Soon</span>
                </div>

                <div className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-600 rounded-lg border border-gray-200">
                  <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
                  </svg>
                  <span className="text-sm font-medium">Android - Coming Soon</span>
                </div>
              </div>
            </div>

            <p className="text-sm text-gray-500 mt-4">
              Flexible subscription plans • Start free • Cancel anytime
            </p>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose SpeechPilot?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Built for professionals who demand accuracy, speed, and privacy in their speech-to-text workflow.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center text-white mr-4">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">{feature.title}</h3>
                </div>
                <p className="text-gray-600">{feature.description}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Platform Support */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Available for Your Platform
            </h2>
            <p className="text-xl text-gray-600">
              Native applications optimized for Windows, macOS, iOS, and Android
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            <Card className="p-8 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M0 3.449L9.75 2.1v9.451H0m10.949-9.602L24 0v11.4H10.949M0 12.6h9.75v9.451L0 20.699M10.949 12.6H24V24l-13.051-1.351"/>
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Windows</h3>
              <p className="text-gray-600 mb-4">
                Optimized for Windows 10 and 11. Seamless integration with Microsoft Office and other productivity tools.
              </p>
              <ul className="text-sm text-gray-500 space-y-1">
                <li>• Windows 10/11 compatible</li>
                <li>• Office integration</li>
                <li>• System-wide hotkeys</li>
              </ul>
            </Card>
            
            <Card className="p-8 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.624 5.367 11.99 11.988 11.99s11.99-5.366 11.99-11.99C24.007 5.367 18.641.001 12.017.001zM8.948 2.684c-.064 0-.125.016-.125.016s.063-.016.125-.016zm2.139-.048c-.016 0-.031 0-.047.016.016-.016.031-.016.047-.016zm.859.032c-.032 0-.063.016-.063.016s.031-.016.063-.016z"/>
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">macOS</h3>
              <p className="text-gray-600 mb-4">
                Native macOS application with full support for macOS features and Apple Silicon processors.
              </p>
              <ul className="text-sm text-gray-500 space-y-1">
                <li>• macOS 11+ compatible</li>
                <li>• Apple Silicon optimized</li>
                <li>• Spotlight integration</li>
              </ul>
            </Card>

            <Card className="p-8 text-center hover:shadow-lg transition-shadow relative">
              <div className="absolute top-4 right-4">
                <span className="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                  Coming Soon
                </span>
              </div>
              <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">iOS</h3>
              <p className="text-gray-600 mb-4">
                Native iOS app with seamless integration across all your Apple devices.
              </p>
              <ul className="text-sm text-gray-500 space-y-1">
                <li>• iOS 15+ compatible</li>
                <li>• iCloud sync</li>
                <li>• Siri integration</li>
              </ul>
            </Card>

            <Card className="p-8 text-center hover:shadow-lg transition-shadow relative">
              <div className="absolute top-4 right-4">
                <span className="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                  Coming Soon
                </span>
              </div>
              <div className="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M17.523 15.3414c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993.0001.5511-.4482.9997-.9993.9997m-5.046 0c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993 0 .5511-.4482.9997-.9993.9997m-5.046 0c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993 0 .5511-.4482.9997-.9993.9997M6.05 13.0583c-.6076-.3825-1.2138-.7705-1.8138-1.1707C2.7779 10.9121 1.5 9.5765 1.5 7.8c0-3.0376 2.7906-5.5 6.2632-5.5s6.2632 2.4624 6.2632 5.5c0 1.7765-1.2779 3.1121-2.7363 4.0876-.6 .4002-1.2062.7882-1.8138 1.1707-.3038.1913-.6076.3825-.9113.5738-.3038-.1913-.6076-.3825-.9113-.5738"/>
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Android</h3>
              <p className="text-gray-600 mb-4">
                Powerful Android app with Google services integration and cross-device sync.
              </p>
              <ul className="text-sm text-gray-500 space-y-1">
                <li>• Android 8+ compatible</li>
                <li>• Google Drive sync</li>
                <li>• Assistant integration</li>
              </ul>
            </Card>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Loved by Professionals
            </h2>
            <p className="text-xl text-gray-600">
              See what our users are saying about SpeechPilot
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="p-6">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-4">&ldquo;{testimonial.content}&rdquo;</p>
                <div>
                  <p className="font-semibold text-gray-900">{testimonial.name}</p>
                  <p className="text-sm text-gray-500">{testimonial.role}</p>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
          <div className="absolute bottom-10 right-10 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
          <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-white/10 rounded-full blur-lg"></div>
        </div>

        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8 relative z-10">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Transform Your Productivity?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Join thousands of professionals who have already made the switch to SpeakOneAI.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            {/* Primary CTA Button */}
            <Button size="lg" asChild className="bg-white text-blue-600 hover:bg-gray-100 font-semibold px-8 py-4 text-lg shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300">
              <Link href="/pricing" className="flex items-center">
                <Zap className="w-5 h-5 mr-2" />
                Get SpeakOneAI Now
              </Link>
            </Button>

            {/* Secondary CTA Button */}
            <Button size="lg" variant="outline" asChild className="border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold px-8 py-4 text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300">
              <Link href="#features" className="flex items-center">
                <Star className="w-5 h-5 mr-2" />
                Learn More
              </Link>
            </Button>
          </div>

          {/* Trust indicators */}
          <div className="flex flex-col sm:flex-row items-center justify-center mt-8 space-y-4 sm:space-y-0 sm:space-x-8 text-blue-100">
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 mr-2 text-green-300" />
              <span className="font-medium">Free to start</span>
            </div>
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 mr-2 text-green-300" />
              <span className="font-medium">Cancel anytime</span>
            </div>
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 mr-2 text-green-300" />
              <span className="font-medium">Instant access</span>
            </div>
          </div>

          {/* Social proof */}
          <div className="mt-8 text-blue-200">
            <p className="text-sm">
              ⭐⭐⭐⭐⭐ Trusted by 10,000+ professionals worldwide
            </p>
          </div>
        </div>
      </section>

      {/* SSO Modal */}
      <SSOModal
        isOpen={showSSOModal}
        onClose={() => setShowSSOModal(false)}
        onSuccess={() => {
          // Redirect to dashboard or pricing page after successful sign in
          window.location.href = '/pricing'
        }}
      />
    </div>
  )
}
